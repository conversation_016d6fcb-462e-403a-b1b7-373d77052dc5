{"greeting": "<PERSON><PERSON>-vindo a<PERSON>", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Ver mais", "seeLess": "<PERSON>er menos", "tokens": "Tokens:", "cache": "Cache:", "apiCost": "Custo da API:", "contextWindow": "<PERSON><PERSON>:", "closeAndStart": "Fechar tarefa e iniciar nova", "export": "Exportar histó<PERSON><PERSON> de <PERSON>fas", "delete": "Excluir tarefa (Shift + Clique para pular confirmação)", "condenseContext": "Condensar contexto de forma inteligente", "share": "Compart<PERSON><PERSON> tarefa", "copyId": "Copiar ID da tarefa", "shareWithOrganization": "Compartilhar com organização", "shareWithOrganizationDescription": "Apenas membros da sua organização podem acessar", "sharePublicly": "Compartilhar publicamente", "sharePubliclyDescription": "Qualquer pessoa com o link pode acessar", "connectToCloud": "Conectar ao Cloud", "connectToCloudDescription": "Entre no zhanlu Cloud para compartilhar tarefas", "sharingDisabledByOrganization": "Compartilhamento desabilitado pela organização", "shareSuccessOrganization": "Link da organização copiado para a área de transferência", "shareSuccessPublic": "Link público copiado para a área de transferência"}, "unpin": "Desfixar", "pin": "Fixar", "tokenProgress": {"availableSpace": "Espaço disponível: {{amount}} tokens", "tokensUsed": "Tokens usados: {{used}} de {{total}}", "reservedForResponse": "Reservado para resposta do modelo: {{amount}} tokens"}, "retry": {"title": "Tentar novamente", "tooltip": "Tentar a operação novamente"}, "startNewTask": {"title": "Iniciar nova tarefa", "tooltip": "Começar uma nova tarefa"}, "proceedAnyways": {"title": "Prosseguir mesmo assim", "tooltip": "Continuar enquanto o comando executa"}, "save": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON> as alterações da mensagem"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Rejeitar esta ação"}, "completeSubtaskAndReturn": "Completar subtarefa e retornar", "approve": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Aprovar esta ação"}, "runCommand": {"title": "Executar comando", "tooltip": "Executar este comando"}, "proceedWhileRunning": {"title": "Prosseguir durante execução", "tooltip": "Continuar apesar dos avisos"}, "killCommand": {"title": "Interromper Comando", "tooltip": "Interromper o comando atual"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON> ta<PERSON>", "tooltip": "Continuar a tarefa atual"}, "terminate": {"title": "Terminar", "tooltip": "Encerrar a tarefa atual"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Cancelar a operação atual"}, "scrollToBottom": "Rolar para o final do chat", "about": "<PERSON><PERSON>, co<PERSON><PERSON>, refatore e depure código com a ajuda do Modelo de IA Zhanlu.<br />Consulte nossa <DocsLink>documentação</DocsLink> para mais informações.", "onboarding": "A lista de tarefas neste espaço de trabalho está vazia. Comece digitando sua tarefa abaixo.<br>Não sabe como começar? Leia mais em nossa <DocsLink>documentação</DocsLink>.", "zhanluTips": {"boomerangTasks": {"title": "Orquestração de Tarefas", "description": "<PERSON><PERSON><PERSON> as tarefas em partes menores e gerenciáveis."}, "stickyModels": {"title": "Modos Fixos", "description": "Cada modo lembra o seu último modelo usado"}, "tools": {"title": "Ferramentas", "description": "Permita que a IA resolva problemas navegando na web, executando comandos e muito mais."}, "customizableModes": {"title": "Modos personalizáveis", "description": "Personas especializadas com comportamentos próprios e modelos atribuídos"}, "architect": {"title": "<PERSON><PERSON>", "description": "Planeje arquiteturas de sistema e projete estruturas de código."}, "code": {"title": "<PERSON><PERSON>", "description": "Gere, modifique e otimize código."}, "unit_test": {"title": "Modo Teste Unitário", "description": "Crie testes abrangentes para garantir estabilidade do código."}, "project_fix": {"title": "Modo Correção de Projeto", "description": "Identifique e corrija problemas ou erros no projeto."}, "security_fix": {"title": "Modo Correção de Segurança", "description": "Identifique e resolva vulnerabilidades de segurança no código."}, "code_review": {"title": "Modo <PERSON> de Código", "description": "Analise a qualidade do código e ofereça sugestões de melhoria."}, "documentation": {"title": "Modo Documentação", "description": "Crie documentação e instruções claras e detalhadas."}, "qa": {"title": "Modo Perguntas e Respostas", "description": "Responda perguntas e forneça assistência simples."}}, "selectMode": "Selecionar modo de interação", "selectApiConfig": "Selecionar configuração da API", "internetSearch": "Depois de abrir a busca na Internet, você pode pesquisar conteúdo relevante na Internet", "internetSearchClosed": "Fechar a busca na Internet", "enhancePrompt": "Aprimorar prompt com contexto adicional", "addImages": "Adicionar imagens à mensagem", "sendMessage": "Enviar mensagem", "stopTts": "Parar conversão de texto em fala", "typeMessage": "Digite uma mensagem...", "typeTask": "Digite sua tarefa aqui...", "addContext": "@ Adicionar contexto, / modo de mud<PERSON>ça, # comando rápido", "dragFiles": "Segure Shift e arraste arquivos", "dragFilesImages": "Segure Shift e arraste arquivos/imagens", "enhancePromptDescription": "O botão 'Aprimorar prompt' ajuda a melhorar seu pedido fornecendo contexto adicional, esclarecimentos ou reformulações. Tente digitar um pedido aqui e clique no botão novamente para ver como funciona.", "modeSelector": {"title": "Modos", "marketplace": "Marketplace de Modos", "settings": "Configurações de Modos", "description": "Personas especializadas que adaptam o comportamento do Zhanlu."}, "errorReadingFile": "Erro ao ler arquivo:", "noValidImages": "Nenhuma imagem válida foi processada", "separator": "Separador", "edit": "Editar...", "forNextMode": "para o próximo modo", "forPreviousMode": "para o modo anterior", "error": "Erro", "warning": "Aviso", "diffError": {"title": "Edição mal-sucedida"}, "troubleMessage": "<PERSON><PERSON><PERSON> está tendo problemas...", "apiRequest": {"title": "Requisição API", "failed": "Requisição API falhou", "streaming": "Requisição API...", "cancelled": "Requisição API cancelada", "streamingFailed": "Streaming API falhou"}, "checkpoint": {"initial": "Ponto de verificação inicial", "regular": "Ponto de verificação", "initializingWarning": "Ainda inicializando ponto de verificação... Se isso demorar muito, você pode desativar os pontos de verificação nas <settingsLink>configurações</settingsLink> e reiniciar sua tarefa.", "menu": {"viewDiff": "Ver diferenças", "restore": "Restaurar ponto de verificação", "restoreFiles": "Restaurar a<PERSON>", "restoreFilesDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto.", "restoreFilesAndTask": "Restaurar arquivos e tarefa", "confirm": "Confirmar", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "Esta ação não pode ser desfeita.", "restoreFilesAndTaskDescription": "Restaura os arquivos do seu projeto para um snapshot feito neste ponto e exclui todas as mensagens após este ponto."}, "current": "Atual"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> quer buscar instruções detalhadas para ajudar com a tarefa atual"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> quer ler este arquivo:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> quer ler este arquivo fora do espaço de trabalho:", "didRead": "<PERSON><PERSON><PERSON> leu este arquivo:", "wantsToEdit": "<PERSON><PERSON><PERSON> quer editar este arquivo:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> quer editar este arquivo fora do espaço de trabalho:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> quer editar um arquivo de configuração protegido:", "wantsToCreate": "<PERSON><PERSON><PERSON> quer criar um novo arquivo:", "wantsToSearchReplace": "Z<PERSON>lu quer realizar busca e substituição neste arquivo:", "didSearchReplace": "Zhanlu realizou busca e substituição neste arquivo:", "wantsToInsert": "<PERSON><PERSON><PERSON> quer inserir conteúdo neste arquivo:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> quer inserir conteúdo neste arquivo na linha {{lineNumber}}:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> quer adicionar conteúdo ao final deste arquivo:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> quer ler este arquivo e mais {{count}}:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> deseja ler múltiplos arquivos:", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> quer aplicar alterações a múltiplos arquivos:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> quer visualizar os arquivos de nível superior neste diretório:", "didViewTopLevel": "<PERSON><PERSON><PERSON> visualizou os arquivos de nível superior neste diretório:", "wantsToViewRecursive": "<PERSON><PERSON><PERSON> quer visualizar recursivamente todos os arquivos neste diretório:", "didViewRecursive": "Zhanlu visualizou recursivamente todos os arquivos neste diretório:", "wantsToViewDefinitions": "<PERSON><PERSON><PERSON> quer visualizar nomes de definição de código-fonte usados neste diretório:", "didViewDefinitions": "<PERSON><PERSON><PERSON> visualizou nomes de definição de código-fonte usados neste diretório:", "wantsToSearch": "<PERSON><PERSON><PERSON> quer pesquisar neste diretório por <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> pesquisou neste diretório por <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> quer pesquisar neste diretório (fora do espaço de trabalho) por <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> pesquisou neste diretório (fora do espaço de trabalho) por <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> quer visualizar os arquivos de nível superior neste diretório (fora do espaço de trabalho):", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> visualizou os arquivos de nível superior neste diretório (fora do espaço de trabalho):", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> quer visualizar recursivamente todos os arquivos neste diretório (fora do espaço de trabalho):", "didViewRecursiveOutsideWorkspace": "Zhanlu visualizou recursivamente todos os arquivos neste diretório (fora do espaço de trabalho):", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> quer visualizar nomes de definição de código-fonte usados neste diretório (fora do espaço de trabalho):", "didViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> visualizou nomes de definição de código-fonte usados neste diretório (fora do espaço de trabalho):"}, "commandOutput": "Saída do comando", "commandExecution": {"running": "Executando", "pid": "PID: {{pid}}", "exited": "Encerrado ({{exitCode}})", "manageCommands": "Gerenciar Permissões de Comando", "commandManagementDescription": "<PERSON><PERSON><PERSON><PERSON> as permissões de comando: Clique em ✓ para permitir a execução automática, ✗ para negar a execução. Os padrões podem ser ativados/desativados ou removidos das listas. <settingsLink>Ver todas as configurações</settingsLink>", "addToAllowed": "Adicionar à lista de permitidos", "removeFromAllowed": "Remover da lista de permitidos", "addToDenied": "Adicionar à lista de negados", "removeFromDenied": "Remover da lista de negados", "abortCommand": "Abortar execução do comando", "expandOutput": "Expan<PERSON>", "collapseOutput": "<PERSON><PERSON><PERSON><PERSON>", "expandManagement": "Expandir seção de gerenciamento de comandos", "collapseManagement": "Recolher seção de gerenciamento de comandos"}, "response": "Resposta", "arguments": "Argumentos", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> quer usar uma ferramenta no servidor MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> quer acessar um recurso no servidor MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> quer mudar para o modo <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> quer mudar para o modo <code>{{mode}}</code> porque: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> para o modo <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> para o modo <code>{{mode}}</code> porque: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> quer criar uma nova subtarefa no modo <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON><PERSON> quer finalizar esta subtarefa", "newTaskContent": "Instruções da subtarefa", "completionContent": "Subtarefa concluída", "resultContent": "Resultados da subtarefa", "defaultResult": "Por favor, continue com a próxima tarefa.", "completionInstructions": "Subtarefa concluída! Você pode revisar os resultados e sugerir correções ou próximos passos. Se tudo parecer bom, confirme para retornar o resultado à tarefa principal."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> tem uma pergunta:"}, "taskCompleted": "Tarefa concluída", "powershell": {"issues": "Parece que você está tendo problemas com o Windows PowerShell, por favor veja este"}, "autoApprove": {"title": "Aprovação automática:", "none": "<PERSON><PERSON><PERSON><PERSON>", "description": "A aprovação automática permite que o zhanlu execute ações sem pedir permissão. Ative apenas para ações nas quais você confia totalmente. Configuração mais detalhada disponível nas <settingsLink>Configurações</settingsLink>.", "selectOptionsFirst": "Selecione pelo menos uma opção abaixo para ativar a aprovação automática", "toggleAriaLabel": "Alternar aprovação automática", "disabledAriaLabel": "Aprovação automática desativada - selecione as opções primeiro"}, "reasoning": {"thinking": "Pensando", "seconds": "{{count}}s"}, "contextCondense": {"title": "Contexto condensado", "condensing": "Condensando contexto...", "errorHeader": "Falha ao condensar contexto", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "Copiar para entrada (ou Shift + clique)", "autoSelectCountdown": "Seleção automática em {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Atualização Zhanlu Versão 2.3.2", "description": "Correções de bugs, autocompletar código", "whatsNew": "Atualizações Importantes", "feature1": "<bold>Modo Fundação de Código adicionado</bold>: O modo Fundação de Código gera exercícios de programação", "feature2": "<bold>Correção de bug de tarefas de histórico</bold>: Problema resolvido onde novas tarefas aparecem nas tarefas de histórico", "feature3": "<bold>Correção do problema de cintilação</bold>: Problema de cintilação ocasional da tela ao mostrar código resolvido", "feature4": "<bold>Outras otimizações</bold>: Otimizações de vários outros problemas", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Consulte a <discordLink>documentação detalhada</discordLink> para conhecer mais recursos 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> quer usar o navegador:", "consoleLogs": "Logs do console", "noNewLogs": "(Sem novos logs)", "screenshot": "Captura de tela do navegador", "cursor": "cursor", "navigation": {"step": "Passo {{current}} de {{total}}", "previous": "Anterior", "next": "Próximo"}, "sessionStarted": "Sessão do navegador iniciada", "actions": {"title": "Ação do navegador: ", "launch": "Iniciar nave<PERSON> em {{url}}", "click": "Clique ({{coordinate}})", "type": "Digitar \"{{text}}\"", "scrollDown": "Rolar para baixo", "scrollUp": "Rolar para cima", "close": "<PERSON><PERSON><PERSON>"}}, "codeblock": {"tooltips": {"expand": "Expandir bloco de código", "collapse": "Re<PERSON>lher bloco de código", "enable_wrap": "Ativar quebra de linha", "disable_wrap": "<PERSON>ati<PERSON> que<PERSON> de linha", "copy_code": "<PERSON><PERSON>r c<PERSON>"}}, "qucikInstructions": {"UiToCode": "Geração de código de desenho de UI", "UmlToCode": "Código de geração de gráficos UML", "ExplainCode": "Interpretação do código", "FixCode": "Correção de código", "ImproveCode": "Otimização de código", "UnitTest": "Teste de unidade", "CODE_REVIEW": "Revisão de código", "CommentCode": "Comentário de código", "PlusButtonClicked": "Esvaziar o diálogo"}, "systemPromptWarning": "AVISO: Substituição personalizada de instrução do sistema ativa. Isso pode comprometer gravemente a funcionalidade e causar comportamento imprevisível.", "profileViolationWarning": "O perfil atual não é compatível com as configurações da sua organização", "shellIntegration": {"title": "Aviso de execução de comando", "description": "Seu comando está sendo executado sem a integração de shell do terminal VSCode. Para suprimir este aviso, você pode desativar a integração de shell na seção <strong>Terminal</strong> das <settingsLink>configurações do Zhanlu</settingsLink> ou solucionar problemas de integração do terminal VSCode usando o link abaixo.", "troubleshooting": "Clique aqui para a documentação de integração de shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Limite de Solicitações Auto-aprovadas Atingido", "description": "<PERSON><PERSON><PERSON> atingiu o limite auto-aprovado de {{count}} solicitação(ões) de API. Deseja redefinir a contagem e prosseguir com a tarefa?", "button": "Redefinir e Continuar"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> quer pesquisar na base de código por <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> quer pesquisar na base de código por <code>{{query}}</code> em <code>{{path}}</code>:", "didSearch_one": "Encontrado 1 resultado", "didSearch_other": "Encontrados {{count}} resultados", "resultTooltip": "Pontuação de similaridade: {{score}} (clique para abrir o arquivo)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON>r tudo"}, "deny": {"title": "<PERSON><PERSON><PERSON> tudo"}}, "indexingStatus": {"ready": "<PERSON><PERSON><PERSON> pronto", "indexing": "Indexando {{percentage}}%", "indexed": "Indexado", "error": "<PERSON>rro do <PERSON>", "status": "Status do índice"}, "versionIndicator": {"ariaLabel": "V<PERSON><PERSON> {{version}} - Clique para ver as notas de lançamento"}, "zhanluCloudCTA": {"title": "<PERSON>hanlu Cloud chegará em breve!", "description": "Execute agentes remotos na nuvem, acesse suas tarefas de qualquer lugar, colabore com outros e muito mais.", "joinWaitlist": "Junte-se à lista de espera para obter acesso antecipado."}, "editMessage": {"placeholder": "Edite sua mensagem..."}}