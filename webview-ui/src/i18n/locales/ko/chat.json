{"greeting": "Zhanlu에 오신 것을 환영합니다", "task": {"title": "작업", "seeMore": "더 보기", "seeLess": "줄여보기", "tokens": "토큰:", "cache": "캐시:", "apiCost": "API 비용:", "contextWindow": "컨텍스트 창:", "closeAndStart": "작업 닫고 새 작업 시작", "export": "작업 기록 내보내기", "delete": "작업 삭제 (Shift + 클릭으로 확인 생략)", "condenseContext": "컨텍스트 지능적으로 압축", "share": "작업 공유", "copyId": "작업 ID 복사", "shareWithOrganization": "조직과 공유", "shareWithOrganizationDescription": "조직 구성원만 액세스할 수 있습니다", "sharePublicly": "공개적으로 공유", "sharePubliclyDescription": "링크가 있는 누구나 액세스할 수 있습니다", "connectToCloud": "클라우드에 연결", "connectToCloudDescription": "작업을 공유하려면 zhanlu Cloud에 로그인하세요", "sharingDisabledByOrganization": "조직에서 공유가 비활성화됨", "shareSuccessOrganization": "조직 링크가 클립보드에 복사되었습니다", "shareSuccessPublic": "공개 링크가 클립보드에 복사되었습니다"}, "unpin": "고정 해제하기", "pin": "고정하기", "tokenProgress": {"availableSpace": "사용 가능한 공간: {{amount}} 토큰", "tokensUsed": "사용된 토큰: {{used}} / {{total}}", "reservedForResponse": "모델 응답용 예약: {{amount}} 토큰"}, "retry": {"title": "다시 시도", "tooltip": "작업 다시 시도"}, "startNewTask": {"title": "새 작업 시작", "tooltip": "새 작업 시작하기"}, "proceedAnyways": {"title": "그래도 계속", "tooltip": "명령 실행 중에도 계속 진행"}, "save": {"title": "저장", "tooltip": "메시지 변경사항 저장"}, "reject": {"title": "거부", "tooltip": "이 작업 거부"}, "completeSubtaskAndReturn": "하위 작업 완료 후 돌아가기", "approve": {"title": "승인", "tooltip": "이 작업 승인"}, "runCommand": {"title": "명령 실행", "tooltip": "이 명령 실행"}, "proceedWhileRunning": {"title": "실행 중에도 계속", "tooltip": "경고에도 불구하고 계속 진행"}, "killCommand": {"title": "명령 종료", "tooltip": "현재 명령 종료"}, "resumeTask": {"title": "작업 재개", "tooltip": "현재 작업 계속하기"}, "terminate": {"title": "종료", "tooltip": "현재 작업 종료"}, "cancel": {"title": "취소", "tooltip": "현재 작업 취소"}, "scrollToBottom": "채팅 맨 아래로 스크롤", "about": "Zhanlu AI 모델의 도움으로 코드를 생성, 수정, 리팩토링 및 디버깅합니다.<br />자세한 정보는 <DocsLink>문서</DocsLink>를 확인하세요.", "onboarding": "이 작업 영역의 태스크 목록이 비어 있습니다. 아래에 태스크를 입력하여 시작하세요.<br>시작 방법을 모르시나요? <DocsLink>문서</DocsLink>에서 자세히 알아보세요.", "zhanluTips": {"architectMode": {"title": "아키텍트 모드", "description": "솔루션 구현을 위한 상세 계획 작성"}, "codeMode": {"title": "코드 모드", "description": "모범 사례에 따라 코드 작성 및 수정"}, "testMode": {"title": "단위 테스트 모드", "description": "코드에 대한 포괄적인 테스트 생성"}, "projectFixMode": {"title": "프로젝트 수정 모드", "description": "프로젝트의 결함 식별 및 수정"}, "sastMode": {"title": "보안 수정 모드", "description": "코드의 보안 취약점 수정"}, "codeReviewMode": {"title": "코드 리뷰 모드", "description": "코드 품질 평가 및 개선 제안"}, "readmeMode": {"title": "문서화 모드", "description": "상세한 기술 문서 작성"}, "simpleMode": {"title": "질의응답 모드", "description": "기술적 질문에 정확히 답변"}, "boomerangTasks": {"title": "작업 오케스트레이션", "description": "작업을 더 작고 관리하기 쉬운 부분으로 나눕니다."}, "stickyModels": {"title": "스티키 모드", "description": "각 모드는 마지막으로 사용한 모델을 기억합니다."}, "tools": {"title": "도구", "description": "AI가 웹 탐색, 명령 실행 등으로 문제를 해결하도록 허용합니다."}, "customizableModes": {"title": "사용자 정의 모드", "description": "고유한 동작과 할당된 모델을 가진 전문 페르소나"}}, "selectMode": "상호작용 모드 선택", "selectApiConfig": "API 구성 선택", "internetSearch": "인터넷 검색을 켜면 인터넷에서 관련 내용을 검색할 수 있습니다", "internetSearchClosed": "인터넷 검색 닫기", "enhancePrompt": "추가 컨텍스트로 프롬프트 향상", "addImages": "메시지에 이미지 추가", "sendMessage": "메시지 보내기", "stopTts": "텍스트 음성 변환 중지", "typeMessage": "메시지 입력...", "typeTask": "여기에 작업 입력...", "addContext": "@ 컨텍스트 추가, / 전환 모드, # 단축 명령", "dragFiles": "Shift 키를 누른 채 파일 드래그", "dragFilesImages": "Shift 키를 누른 채 파일/이미지 드래그", "enhancePromptDescription": "'프롬프트 향상' 버튼은 추가 컨텍스트, 명확화 또는 재구성을 제공하여 요청을 개선합니다. 여기에 요청을 입력한 다음 버튼을 다시 클릭하여 작동 방식을 확인해보세요.", "modeSelector": {"title": "모드", "marketplace": "모드 마켓플레이스", "settings": "모드 설정", "description": "<PERSON><PERSON><PERSON>의 행동을 맞춤화하는 전문화된 페르소나."}, "errorReadingFile": "파일 읽기 오류:", "noValidImages": "처리된 유효한 이미지가 없습니다", "separator": "구분자", "edit": "편집...", "forNextMode": "다음 모드용", "forPreviousMode": "이전 모드용", "error": "오류", "warning": "경고", "diffError": {"title": "편집 실패"}, "troubleMessage": "Zhanlu에 문제가 발생했습니다...", "apiRequest": {"title": "API 요청", "failed": "API 요청 실패", "streaming": "API 요청...", "cancelled": "API 요청 취소됨", "streamingFailed": "API 스트리밍 실패"}, "checkpoint": {"initial": "초기 체크포인트", "regular": "체크포인트", "initializingWarning": "체크포인트 초기화 중... 시간이 너무 오래 걸리면 <settingsLink>설정</settingsLink>에서 체크포인트를 비활성화하고 작업을 다시 시작할 수 있습니다.", "menu": {"viewDiff": "차이점 보기", "restore": "체크포인트 복원", "restoreFiles": "파일 복원", "restoreFilesDescription": "프로젝트 파일을 이 시점에 찍힌 스냅샷으로 복원합니다.", "restoreFilesAndTask": "파일 및 작업 복원", "confirm": "확인", "cancel": "취소", "cannotUndo": "이 작업은 취소할 수 없습니다.", "restoreFilesAndTaskDescription": "프로젝트 파일을 이 시점에 찍힌 스냅샷으로 복원하고 이 지점 이후의 모든 메시지를 삭제합니다."}, "current": "현재"}, "instructions": {"wantsToFetch": "Zhanlu는 현재 작업을 지원하기 위해 자세한 지침을 가져오려고 합니다"}, "fileOperations": {"wantsToRead": "Zhanlu가 이 파일을 읽고 싶어합니다:", "wantsToReadOutsideWorkspace": "Zhanlu가 워크스페이스 외부의 이 파일을 읽고 싶어합니다:", "didRead": "Zhanlu가 이 파일을 읽었습니다:", "wantsToEdit": "Zhanlu가 이 파일을 편집하고 싶어합니다:", "wantsToEditOutsideWorkspace": "Zhanlu가 워크스페이스 외부의 이 파일을 편집하고 싶어합니다:", "wantsToEditProtected": "Zhanlu가 보호된 설정 파일을 편집하고 싶어합니다:", "wantsToCreate": "Zhanlu가 새 파일을 만들고 싶어합니다:", "wantsToSearchReplace": "Roo가 이 파일에서 검색 및 바꾸기를 수행하고 싶어합니다:", "didSearchReplace": "Roo가 이 파일에서 검색 및 바꾸기를 수행했습니다:", "wantsToInsert": "Roo가 이 파일에 내용을 삽입하고 싶어합니다:", "wantsToInsertWithLineNumber": "Roo가 이 파일의 {{lineNumber}}번 줄에 내용을 삽입하고 싶어합니다:", "wantsToInsertAtEnd": "Roo가 이 파일의 끝에 내용을 추가하고 싶어합니다:", "wantsToReadAndXMore": "Roo가 이 파일과 {{count}}개의 파일을 더 읽으려고 합니다:", "wantsToReadMultiple": "Roo가 여러 파일을 읽으려고 합니다:", "wantsToApplyBatchChanges": "Roo가 여러 파일에 변경 사항을 적용하고 싶어합니다:"}, "directoryOperations": {"wantsToViewTopLevel": "Zhanlu가 이 디렉토리의 최상위 파일을 보고 싶어합니다:", "didViewTopLevel": "Zhanlu가 이 디렉토리의 최상위 파일을 보았습니다:", "wantsToViewRecursive": "Zhanlu가 이 디렉토리의 모든 파일을 재귀적으로 보고 싶어합니다:", "didViewRecursive": "Zhanlu가 이 디렉토리의 모든 파일을 재귀적으로 보았습니다:", "wantsToViewDefinitions": "Zhanlu가 이 디렉토리에서 사용된 소스 코드 정의 이름을 보고 싶어합니다:", "didViewDefinitions": "Zhanlu가 이 디렉토리에서 사용된 소스 코드 정의 이름을 보았습니다:", "wantsToSearch": "Zhanlu가 이 디렉토리에서 <code>{{regex}}</code>을(를) 검색하고 싶어합니다:", "didSearch": "Zhanlu가 이 디렉토리에서 <code>{{regex}}</code>을(를) 검색했습니다:", "wantsToSearchOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)에서 <code>{{regex}}</code>을(를) 검색하고 싶어합니다:", "didSearchOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)에서 <code>{{regex}}</code>을(를) 검색했습니다:", "wantsToViewTopLevelOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)의 최상위 파일을 보고 싶어합니다:", "didViewTopLevelOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)의 최상위 파일을 보았습니다:", "wantsToViewRecursiveOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)의 모든 파일을 재귀적으로 보고 싶어합니다:", "didViewRecursiveOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)의 모든 파일을 재귀적으로 보았습니다:", "wantsToViewDefinitionsOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)에서 사용된 소스 코드 정의 이름을 보고 싶어합니다:", "didViewDefinitionsOutsideWorkspace": "Zhanlu가 이 디렉토리(워크스페이스 외부)에서 사용된 소스 코드 정의 이름을 보았습니다:"}, "commandOutput": "명령 출력", "commandExecution": {"running": "실행 중", "pid": "PID: {{pid}}", "exited": "종료됨 ({{exitCode}})", "manageCommands": "명령 권한 관리", "commandManagementDescription": "명령 권한 관리: 자동 실행을 허용하려면 ✓를 클릭하고 실행을 거부하려면 ✗를 클릭하십시오. 패턴은 켜거나 끄거나 목록에서 제거할 수 있습니다. <settingsLink>모든 설정 보기</settingsLink>", "addToAllowed": "허용 목록에 추가", "removeFromAllowed": "허용 목록에서 제거", "addToDenied": "거부 목록에 추가", "removeFromDenied": "거부 목록에서 제거", "abortCommand": "명령 실행 중단", "expandOutput": "출력 확장", "collapseOutput": "출력 축소", "expandManagement": "명령 관리 섹션 확장", "collapseManagement": "명령 관리 섹션 축소"}, "response": "응답", "arguments": "인수", "mcp": {"wantsToUseTool": "Zhanlu가 {{server<PERSON>ame}} MCP 서버에서 도구를 사용하고 싶어합니다:", "wantsToAccessResource": "Zhanlu가 {{server<PERSON>ame}} MCP 서버에서 리소스에 접근하고 싶어합니다:"}, "modes": {"wantsToSwitch": "Zhanlu가 <code>{{mode}}</code> 모드로 전환하고 싶어합니다", "wantsToSwitchWithReason": "Zhanlu가 다음 이유로 <code>{{mode}}</code> 모드로 전환하고 싶어합니다: {{reason}}", "didSwitch": "Zhanlu가 <code>{{mode}}</code> 모드로 전환했습니다", "didSwitchWithReason": "Zhanlu가 다음 이유로 <code>{{mode}}</code> 모드로 전환했습니다: {{reason}}"}, "subtasks": {"wantsToCreate": "Zhanlu가 <code>{{mode}}</code> 모드에서 새 하위 작업을 만들고 싶어합니다:", "wantsToFinish": "Zhanlu가 이 하위 작업을 완료하고 싶어합니다", "newTaskContent": "하위 작업 지침", "completionContent": "하위 작업 완료", "resultContent": "하위 작업 결과", "defaultResult": "다음 작업을 계속 진행해주세요.", "completionInstructions": "하위 작업 완료! 결과를 검토하고 수정 사항이나 다음 단계를 제안할 수 있습니다. 모든 것이 괜찮아 보이면, 부모 작업에 결과를 반환하기 위해 확인해주세요."}, "questions": {"hasQuestion": "Zhanlu에게 질문이 있습니다:"}, "taskCompleted": "작업 완료", "powershell": {"issues": "Windows PowerShell에 문제가 있는 것 같습니다. 다음을 참조하세요"}, "autoApprove": {"title": "자동 승인:", "none": "없음", "description": "자동 승인을 사용하면 zhanlu가 권한을 요청하지 않고 작업을 수행할 수 있습니다. 완전히 신뢰할 수 있는 작업에만 활성화하세요. 더 자세한 구성은 <settingsLink>설정</settingsLink>에서 사용할 수 있습니다.", "selectOptionsFirst": "자동 승인을 활성화하려면 아래 옵션 중 하나 이상을 선택하세요", "toggleAriaLabel": "자동 승인 전환", "disabledAriaLabel": "자동 승인 비활성화됨 - 먼저 옵션을 선택하세요"}, "reasoning": {"thinking": "생각 중", "seconds": "{{count}}초"}, "contextCondense": {"title": "컨텍스트 요약됨", "condensing": "컨텍스트 압축 중...", "errorHeader": "컨텍스트 압축 실패", "tokens": "토큰"}, "followUpSuggest": {"copyToInput": "입력창에 복사 (또는 Shift + 클릭)", "autoSelectCountdown": "{{count}}초 후 자동 선택", "countdownDisplay": "{{count}}초"}, "announcement": {"title": "🎉 잔루 2.3.2 버전 업데이트", "description": "버그 수정, 코드 자동완성", "whatsNew": "중요 업데이트", "feature1": "<bold>코드 기반 모드 추가</bold>: 코드 기반 모드가 프로그래밍 연습 문제를 생성", "feature2": "<bold>히스토리 작업 버그 수정</bold>: 새 작업이 히스토리 작업에 표시되는 문제 해결", "feature3": "<bold>깜빡임 문제 수정</bold>: 코드 표시 시 간헐적인 화면 깜빡임 표시 문제 해결", "feature4": "<bold>기타 최적화</bold>: 기타 문제의 최적화", "hideButton": "공지 숨기기", "detailsDiscussLinks": "<discordLink>상세 문서</discordLink>에서 더 많은 기능 확인 🚀"}, "browser": {"rooWantsToUse": "Zhanlu가 브라우저를 사용하고 싶어합니다:", "consoleLogs": "콘솔 로그", "noNewLogs": "(새 로그 없음)", "screenshot": "브라우저 스크린샷", "cursor": "커서", "navigation": {"step": "단계 {{current}} / {{total}}", "previous": "이전", "next": "다음"}, "sessionStarted": "브라우저 세션 시작됨", "actions": {"title": "브라우저 작업: ", "launch": "{{url}}에서 브라우저 실행", "click": "클릭 ({{coordinate}})", "type": "입력 \"{{text}}\"", "scrollDown": "아래로 스크롤", "scrollUp": "위로 스크롤", "close": "브라우저 닫기"}}, "codeblock": {"tooltips": {"expand": "코드 블록 확장", "collapse": "코드 블록 축소", "enable_wrap": "자동 줄바꿈 활성화", "disable_wrap": "자동 줄바꿈 비활성화", "copy_code": "코드 복사"}}, "qucikInstructions": {"UiToCode": "UI 설계도 생성 코드", "UmlToCode": "UML 그림 생성 코드", "ExplainCode": "코드 해석", "FixCode": "코드 오류 수정", "ImproveCode": "코드 최적화", "UnitTest": "유닛 테스트", "CODE_REVIEW": "코드 검토", "CommentCode": "코드 주석", "PlusButtonClicked": "대화 상자 비우기"}, "systemPromptWarning": "경고: 사용자 정의 시스템 프롬프트 재정의가 활성화되었습니다. 이로 인해 기능이 심각하게 손상되고 예측할 수 없는 동작이 발생할 수 있습니다.", "profileViolationWarning": "현재 프로필이 조직 설정과 호환되지 않습니다", "shellIntegration": {"title": "명령 실행 경고", "description": "명령이 VSCode 터미널 쉘 통합 없이 실행되고 있습니다. 이 경고를 숨기려면 <settingsLink><PERSON><PERSON><PERSON> 설정</settingsLink>의 <strong>Terminal</strong> 섹션에서 쉘 통합을 비활성화하거나 아래 링크를 사용하여 VSCode 터미널 통합 문제를 해결하세요.", "troubleshooting": "쉘 통합 문서를 보려면 여기를 클릭하세요."}, "ask": {"autoApprovedRequestLimitReached": {"title": "자동 승인 요청 한도 도달", "description": "Roo가 {{count}}개의 API 요청(들)에 대한 자동 승인 한도에 도달했습니다. 카운트를 재설정하고 작업을 계속하시겠습니까?", "button": "재설정 후 계속"}}, "codebaseSearch": {"wantsToSearch": "Roo가 코드베이스에서 <code>{{query}}</code>을(를) 검색하고 싶어합니다:", "wantsToSearchWithPath": "Roo가 <code>{{path}}</code>에서 <code>{{query}}</code>을(를) 검색하고 싶어합니다:", "didSearch_one": "1개의 결과를 찾았습니다", "didSearch_other": "{{count}}개의 결과를 찾았습니다", "resultTooltip": "유사도 점수: {{score}} (클릭하여 파일 열기)"}, "read-batch": {"approve": {"title": "모두 승인"}, "deny": {"title": "모두 거부"}}, "indexingStatus": {"ready": "인덱스 준비됨", "indexing": "인덱싱 중 {{percentage}}%", "indexed": "인덱싱 완료", "error": "인덱스 오류", "status": "인덱스 상태"}, "versionIndicator": {"ariaLabel": "버전 {{version}} - 릴리스 노트를 보려면 클릭하세요"}, "zhanluCloudCTA": {"title": "zhanlu Cloud가 곧 출시됩니다!", "description": "클라우드에서 원격 에이전트를 실행하고, 어디서나 작업에 액세스하고, 다른 사람들과 협업하는 등 다양한 기능을 이용하세요.", "joinWaitlist": "얼리 액세스를 받으려면 대기 목록에 가입하세요."}, "editMessage": {"placeholder": "메시지 편집..."}}