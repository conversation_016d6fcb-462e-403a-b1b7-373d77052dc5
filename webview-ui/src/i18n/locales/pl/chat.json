{"greeting": "Witamy w <PERSON>lu", "task": {"title": "<PERSON><PERSON><PERSON>", "seeMore": "Zobacz więcej", "seeLess": "Zobacz mniej", "tokens": "Tokeny:", "cache": "<PERSON><PERSON><PERSON>ć podręczna:", "apiCost": "Koszt API:", "contextWindow": "Okno kontekstu:", "closeAndStart": "Zamknij zadanie i rozpocznij nowe", "export": "Eksportuj historię zadań", "delete": "<PERSON><PERSON><PERSON> zadanie (Shi<PERSON> + <PERSON><PERSON><PERSON>, aby pomin<PERSON> potwierdzenie)", "condenseContext": "Inteligentnie skondensuj kontekst", "share": "Udostępnij zadanie", "copyId": "Kopiuj ID zadania", "shareWithOrganization": "Udostępnij organizacji", "shareWithOrganizationDescription": "Tylko członkowie twojej organizacji mogą uzyskać dostęp", "sharePublicly": "Udostępnij publicznie", "sharePubliclyDescription": "Każ<PERSON> z linkiem może uzyskać dostęp", "connectToCloud": "Połącz z chmurą", "connectToCloudDescription": "Zaloguj się do zhanlu Cloud, aby udostępniać zadania", "sharingDisabledByOrganization": "Udostępnianie wyłączone przez organizację", "shareSuccessOrganization": "Link organizacji skopiowany do schowka", "shareSuccessPublic": "Link publiczny skopiowany do schowka"}, "unpin": "Odepnij", "pin": "Przypnij", "tokenProgress": {"availableSpace": "Dostę<PERSON><PERSON> miej<PERSON>ce: {{amount}} tokenów", "tokensUsed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tokeny: {{used}} z {{total}}", "reservedForResponse": "Zarezerwowane dla odpowiedzi modelu: {{amount}} tokenów"}, "retry": {"title": "Ponów", "tooltip": "Spróbuj ponownie wykonać operację"}, "startNewTask": {"title": "Rozpocznij nowe zadanie", "tooltip": "Rozpocznij nowe zadanie"}, "proceedAnyways": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> mimo to", "tooltip": "Kontynuuj podczas wykonywania polecenia"}, "save": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "Zapisz zmiany wiadomości"}, "reject": {"title": "<PERSON><PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tę akcję"}, "completeSubtaskAndReturn": "Zakończ podzadanie i wróć", "approve": {"title": "Zatwierdź", "tooltip": "Zatwierdź tę akcję"}, "runCommand": {"title": "Uru<PERSON><PERSON> polecenie", "tooltip": "<PERSON><PERSON><PERSON><PERSON> to polecenie"}, "proceedWhileRunning": {"title": "Kontynuuj podczas wykonywania", "tooltip": "Kontynuuj pomimo ostrzeżeń"}, "killCommand": {"title": "Zatrzymaj <PERSON>nie", "tooltip": "Zatrzymaj bieżące polecenie"}, "resumeTask": {"title": "Wznów zadanie", "tooltip": "Kontynuuj bieżące zadanie"}, "terminate": {"title": "Zakończ", "tooltip": "Zakończ bieżące zadanie"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "Anuluj bieżącą operację"}, "scrollToBottom": "Przewiń do dołu czatu", "about": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, refaktoryzuj i debuguj kod z pomocą Modelu AI Zhanlu.<br />Sprawdź naszą <DocsLink>dokumentację</DocsLink>, aby u<PERSON><PERSON> więcej informacji.", "onboarding": "Lista zadań w tej przestrzeni roboczej jest pusta. Zacznij od wpisania swojego zadania poniżej.<br>Nie wiesz jak zacząć? Przeczytaj więcej w naszej <DocsLink>dokumentacji</DocsLink>.", "zhanluTips": {"boomerangTasks": {"title": "Orkiestracja Zadań", "description": "Podziel zadania na mniejsze, łatwiejsze do zarządzania części."}, "stickyModels": {"title": "Tryby trwałe", "description": "<PERSON><PERSON><PERSON> tryb zapamiętuje ostatnio używany model"}, "tools": {"title": "Narzędzia", "description": "Pozwól sztucznej inteligencji rozwiązywać problemy, prz<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sie<PERSON>, uruchamiając polecenia i nie tylko."}, "customizableModes": {"title": "Konfigurow<PERSON>ne tryby", "description": "Wyspecjalizowane persona z własnymi zachowaniami i przypisanymi modelami"}, "architect": {"title": "Tryb Architekta", "description": "Planowanie architektury systemu i projektowanie struktury kodu."}, "code": {"title": "<PERSON><PERSON>", "description": "Generowanie, modyfikowanie i optymalizacja kodu."}, "unit_test": {"title": "Tryb Testów Jednostkowych", "description": "Tworzenie kompleksowych testów zapewniających stabilność kodu."}, "project_fix": {"title": "<PERSON>b Naprawy Pro<PERSON>u", "description": "Identyfikowanie i naprawianie problemów lub błędów w projekcie."}, "security_fix": {"title": "Tryb Naprawy Bezpieczeństwa", "description": "Identyfikowanie i rozwiązywanie luk bezpieczeństwa w kodzie."}, "code_review": {"title": "Tryb Przeglądu Kodu", "description": "Analiza jakości kodu i propozycje ulepszeń."}, "documentation": {"title": "<PERSON>b Dokumentacji", "description": "Tworzenie jasnej i szczegółowej dokumentacji oraz instrukcji."}, "qa": {"title": "Tryb Pytań i Odpowiedzi", "description": "Odpowiadanie na pytania i udzielanie prostej pomocy."}}, "selectMode": "<PERSON><PERSON><PERSON><PERSON> tryb interakcji", "selectApiConfig": "<PERSON><PERSON><PERSON><PERSON> konfigurację <PERSON>", "internetSearch": "Po otwarciu wyszukiwania internetowego możesz wyszukać odpowiednie treści w Internecie", "internetSearchClosed": "Zamknij wyszukiwanie w Internecie", "enhancePrompt": "Ulepsz podpowiedź dodatkowym kontekstem", "addImages": "Dodaj obrazy do wiadomości", "sendMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stopTts": "Zatrzymaj syntezę mowy", "typeMessage": "<PERSON><PERSON><PERSON> wiadom<PERSON>ść...", "typeTask": "Wpisz swoje zadanie tutaj...", "addContext": "@ Dodaj kontekst, / prze<PERSON><PERSON><PERSON> tryb, # skr<PERSON>t", "dragFiles": "Przytrzymaj Shift i przeciągnij pliki", "dragFilesImages": "Przytrzymaj Shift i przeciągnij pliki/obrazy", "enhancePromptDescription": "Przycisk 'Uleps<PERSON> podpowiedź' pomaga ul<PERSON>, dostar<PERSON><PERSON><PERSON><PERSON> dodatkowy kontekst, wyjaśnienia lub przeformułowania. Spróbuj wpisać prośbę tutaj i kliknij przycisk ponownie, aby <PERSON><PERSON><PERSON>, jak to dzia<PERSON>.", "modeSelector": {"title": "Tryby", "marketplace": "Marketplace Trybów", "settings": "Ustawienia Trybów", "description": "Wyspecjalizowan<PERSON> persony, które dostosowują zachowanie <PERSON>lu."}, "errorReadingFile": "Błąd odczytu pliku:", "noValidImages": "Nie przetworzono żadnych prawidłowych obrazów", "separator": "Separator", "edit": "Edytuj...", "forNextMode": "dla następnego trybu", "forPreviousMode": "dla poprzedniego trybu", "error": "Błąd", "warning": "Ostrzeżenie", "diffError": {"title": "<PERSON><PERSON><PERSON><PERSON>a"}, "troubleMessage": "<PERSON><PERSON><PERSON> ma problemy...", "apiRequest": {"title": "Zapytanie API", "failed": "Zapytanie API nie powiodło się", "streaming": "Zapytanie API...", "cancelled": "Zapytanie API anulowane", "streamingFailed": "Strumieniowanie API nie powiodło się"}, "checkpoint": {"initial": "Początkowy punkt kontrolny", "regular": "Punkt kontrolny", "initializingWarning": "Trwa inicjalizacja punktu kontrolnego... <PERSON><PERSON><PERSON> to trwa zbyt długo, moż<PERSON>z wyłączyć punkty kontrolne w <settingsLink>ustawieniach</settingsLink> i uruchomić zadanie ponownie.", "menu": {"viewDiff": "Zobacz różnice", "restore": "Przywróć punkt kontrolny", "restoreFiles": "P<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> pliki", "restoreFilesDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie.", "restoreFilesAndTask": "Przywróć pliki i zadanie", "confirm": "Potwierdź", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON> akcji nie można <PERSON>.", "restoreFilesAndTaskDescription": "Przywraca pliki Twojego projektu do zrzutu wykonanego w tym punkcie i usuwa wszystkie wiadomości po tym punkcie."}, "current": "Bieżący"}, "instructions": {"wantsToFetch": "Z<PERSON><PERSON> chce pobrać szczegółowe instrukcje, aby pomóc w bieżącym zadaniu"}, "fileOperations": {"wantsToRead": "Z<PERSON>lu chce przeczytać ten plik:", "wantsToReadOutsideWorkspace": "Z<PERSON><PERSON> chce przeczytać ten plik poza obszarem roboczym:", "didRead": "Z<PERSON>lu przeczytał ten plik:", "wantsToEdit": "<PERSON><PERSON><PERSON> chce edytować ten plik:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> chce edytować ten plik poza obszarem roboczym:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> chce edytować chroniony plik konfiguracyjny:", "wantsToCreate": "Zhanlu chce utworzyć nowy plik:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> chce wykonać wyszukiwanie i zamianę w tym pliku:", "didSearchReplace": "Zhanlu wykonał wyszukiwanie i zamianę w tym pliku:", "wantsToInsert": "Zhanlu chce wstawić zawartość do tego pliku:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> chce w<PERSON>wić zawartość do tego pliku w linii {{lineNumber}}:", "wantsToInsertAtEnd": "Z<PERSON><PERSON> chce dodać zawartość na końcu tego pliku:", "wantsToReadMultiple": "Z<PERSON>lu chce odczytać wiele plików:", "wantsToApplyBatchChanges": "Zhanlu chce zastosować zmiany do wielu plików:"}, "directoryOperations": {"wantsToViewTopLevel": "Z<PERSON>lu chce zobaczyć pliki najwyższego poziomu w tym katalogu:", "didViewTopLevel": "Zhanlu zobaczył pliki najwyższego poziomu w tym katalogu:", "wantsToViewRecursive": "Z<PERSON>lu chce rekurencyjnie zobaczyć wszystkie pliki w tym katalogu:", "didViewRecursive": "Zhanlu rekurencyjnie zobaczył wszystkie pliki w tym katalogu:", "wantsToViewDefinitions": "<PERSON><PERSON><PERSON> chce zobaczyć nazwy definicji kodu źródłowego używane w tym katalogu:", "didViewDefinitions": "<PERSON><PERSON>lu zobaczył nazwy definicji kodu źródłowego używane w tym katalogu:", "wantsToSearch": "<PERSON><PERSON><PERSON> chce przeszukać ten katalog w poszukiwaniu <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> przeszukał ten katalog w poszukiwaniu <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> chce przeszukać ten katalog (poza obszarem roboczym) w poszukiwaniu <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON>lu przeszukał ten katalog (poza obszarem roboczym) w poszukiwaniu <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "Z<PERSON>lu chce zobaczyć pliki najwyższego poziomu w tym katalogu (poza obszarem roboczym):", "didViewTopLevelOutsideWorkspace": "Zhanlu zobaczył pliki najwyższego poziomu w tym katalogu (poza obszarem roboczym):", "wantsToViewRecursiveOutsideWorkspace": "Z<PERSON>lu chce rekurencyjnie zobaczyć wszystkie pliki w tym katalogu (poza obszarem roboczym):", "didViewRecursiveOutsideWorkspace": "Zhanlu rekurencyjnie zobaczył wszystkie pliki w tym katalogu (poza obszarem roboczym):", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON>lu chce zobaczyć nazwy definicji kodu źródłowego używane w tym katalogu (poza obszarem roboczym):", "didViewDefinitionsOutsideWorkspace": "Zhanlu zobaczył nazwy definicji kodu źródłowego używane w tym katalogu (poza obszarem roboczym):"}, "commandOutput": "Wyjście polecenia", "commandExecution": {"running": "Wykonywanie", "pid": "PID: {{pid}}", "exited": "Zakończono ({{exitCode}})", "manageCommands": "Zarządzaj uprawnieniami poleceń", "commandManagementDescription": "Zarządzaj uprawnieniami poleceń: K<PERSON><PERSON>j ✓, aby zezwolić na automatyczne wykonanie, ✗, aby od<PERSON>ówić wykonania. Wzorce można włączać/wył<PERSON><PERSON><PERSON><PERSON> lub usuwać z listy. <settingsLink>Zobacz wszystkie ustawienia</settingsLink>", "addToAllowed": "Dodaj do listy dozwolonych", "removeFromAllowed": "Usuń z listy dozwolonych", "addToDenied": "Dodaj do listy odrzuconych", "removeFromDenied": "Usuń z listy odrzuconych", "abortCommand": "Przerwij wykonywanie polecenia", "expandOutput": "Rozwiń wyjście", "collapseOutput": "Zwiń wyjście", "expandManagement": "Rozwiń sekcję zarządzania poleceniami", "collapseManagement": "Zwiń sekcję zarządzania poleceniami"}, "response": "<PERSON><PERSON><PERSON><PERSON><PERSON>ź", "arguments": "Argumenty", "mcp": {"wantsToUseTool": "Zhanlu chce użyć narzędzia na serwerze MCP {{serverName}}:", "wantsToAccessResource": "Zhanlu chce uzyskać dostęp do zasobu na serwerze MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> chce przełączyć się na tryb <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> chce przełączyć się na tryb <code>{{mode}}</code> ponieważ: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> przełączył się na tryb <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> przełączył się na tryb <code>{{mode}}</code> ponieważ: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> chce utworzyć nowe podzadanie w trybie <code>{{mode}}</code>:", "wantsToFinish": "Zhanlu chce zakończyć to podzadanie", "newTaskContent": "Instrukcje podzadania", "completionContent": "Podzadanie zakończone", "resultContent": "Wyniki podzadania", "defaultResult": "Proszę kontynuować następne zadanie.", "completionInstructions": "Podzadanie zakończone! Możesz przejrzeć wyniki i zasugerować poprawki lub następne kroki. Jeśli wszystko wygląda dobrze, pot<PERSON><PERSON><PERSON>, aby zwrócić wynik do zadania nadrzędnego."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> ma pytanie:"}, "taskCompleted": "Zadanie zakończone", "powershell": {"issues": "Wygląda na to, że masz problemy z Windows PowerShell, proszę zapoznaj się z tym"}, "autoApprove": {"title": "Automatyczne zatwierdzanie:", "none": "Brak", "description": "Automatyczne zatwierdzanie pozwala zhanlu wykonywać działania bez pytania o pozwolenie. Włącz tylko dla działań, którym w pełni ufasz. Bardziej szczegółowa konfiguracja dostępna w <settingsLink>Ustawieniach</settingsLink>.", "selectOptionsFirst": "Wybierz co najmniej jedną opcję poniżej, aby włączyć automatyczne zatwierdzanie", "toggleAriaLabel": "Przełącz automatyczne zatwierdzanie", "disabledAriaLabel": "Automatyczne zatwierdzanie wyłączone - najpierw wybierz opcje"}, "reasoning": {"thinking": "<PERSON><PERSON><PERSON><PERSON>", "seconds": "{{count}} s"}, "contextCondense": {"title": "Kontekst skondensowany", "condensing": "Kondensowanie kontekstu...", "errorHeader": "<PERSON><PERSON> udało się skondensować kontekstu", "tokens": "tokeny"}, "followUpSuggest": {"copyToInput": "Kopiuj do pola wprowadzania (lub Shift + kliknięcie)", "autoSelectCountdown": "Automatyczny wybór za {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 Aktualizacja Zhanlu Wersja 2.3.2", "description": "Poprawki błędów, autouzupełnianie kodu", "whatsNew": "Ważne Aktualizacje", "feature1": "<bold><PERSON><PERSON><PERSON> tryb Fundacji Kodu</bold>: Tryb Fundacji Kodu generuje ćwiczenia programistyczne", "feature2": "<bold>Poprawka błędu zadań historii</bold>: <PERSON>, gdzie nowe zadania pojawiają się w zadaniach historii", "feature3": "<bold>Poprawka problemu migotania</bold>: Problem sporadycznego migotania ekranu podczas wyświetlania kodu rozwiązany", "feature4": "<bold><PERSON>e optymal<PERSON></bold>: Optymalizacje różnych innych problemów", "hideButton": "<PERSON><PERSON><PERSON><PERSON>", "detailsDiscussLinks": "Sprawdź <discordLink>szczegółową dokumentację</discordLink> aby poznać więcej funk<PERSON> 🚀"}, "browser": {"rooWantsToUse": "Zhanlu chce użyć przeglądarki:", "consoleLogs": "<PERSON><PERSON> k<PERSON>", "noNewLogs": "(Brak nowych logów)", "screenshot": "Zrzut ekranu przeglądarki", "cursor": "kursor", "navigation": {"step": "<PERSON>rok {{current}} z {{total}}", "previous": "Poprzedni", "next": "Następny"}, "sessionStarted": "Sesja przeglądarki rozpoczęta", "actions": {"title": "Akcja przeglądarki: ", "launch": "Uruchom przeglądarkę na {{url}}", "click": "<PERSON><PERSON><PERSON>j ({{coordinate}})", "type": "Wpisz \"{{text}}\"", "scrollDown": "Przewiń w dół", "scrollUp": "Przewiń w górę", "close": "Zamknij przeglądarkę"}}, "codeblock": {"tooltips": {"expand": "Rozwiń blok kodu", "collapse": "Zwiń blok kodu", "enable_wrap": "Włącz zawijanie wierszy", "disable_wrap": "Wyłącz zawijanie wierszy", "copy_code": "<PERSON><PERSON><PERSON><PERSON> kod"}}, "qucikInstructions": {"UiToCode": "Generowanie kodu UI", "UmlToCode": "Kod generowania wykresów UML", "ExplainCode": "Interpretacja kodu", "FixCode": "<PERSON><PERSON> bł<PERSON>dy", "ImproveCode": "Optymalizacja kodu", "UnitTest": "<PERSON><PERSON>", "CODE_REVIEW": "Przegląd kodu", "CommentCode": "Komentarz kodu", "PlusButtonClicked": "<PERSON><PERSON><PERSON><PERSON><PERSON>ć okno dialogowe"}, "systemPromptWarning": "OSTRZEŻENIE: Aktywne niestandardowe zastąpienie instrukcji systemowych. <PERSON>że to poważnie zakłócić funkcjonalność i powodować nieprzewidywalne zachowanie.", "profileViolationWarning": "Bieżący profil nie jest kompatybilny z ustawieniami Twojej organizacji", "shellIntegration": {"title": "Ostrzeżenie wykonania polecenia", "description": "<PERSON>je polecenie jest wykonywane bez integracji powłoki terminala VSCode. Aby ukryć to ostrzeżenie, moż<PERSON>z wyłączyć integrację powłoki w sekcji <strong>Terminal</strong> w <settingsLink>ustawi<PERSON><PERSON></settingsLink> lub rozwi<PERSON> problemy z integracją terminala VSCode korzystając z poniższego linku.", "troubleshooting": "<PERSON><PERSON><PERSON><PERSON> tutaj, aby zobaczyć dokumentację integracji powłoki."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Osiągnięto limit automatycznie zatwierdzonych żądań", "description": "zhanlu osiągnął automatycznie zatwierdzony limit {{count}} żądania/żądań API. <PERSON>zy chcesz zresetować licznik i kontynuować zadanie?", "button": "Zresetuj i kontynuuj"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> chce przeszukać bazę kodu w poszukiwaniu <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> chce przeszukać bazę kodu w poszukiwaniu <code>{{query}}</code> w <code>{{path}}</code>:", "didSearch_one": "Znaleziono 1 wynik", "didSearch_other": "Znaleziono {{count}} wyników", "resultTooltip": "Wynik podobieństwa: {{score}} (k<PERSON><PERSON><PERSON>, aby otworz<PERSON> plik)"}, "read-batch": {"approve": {"title": "Zatwierdź wszystko"}, "deny": {"title": "<PERSON><PERSON><PERSON><PERSON> wszystko"}}, "indexingStatus": {"ready": "<PERSON><PERSON><PERSON> gotowy", "indexing": "Indeksowanie {{percentage}}%", "indexed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "error": "Błąd indeksu", "status": "Status indeksu"}, "versionIndicator": {"ariaLabel": "Wersja {{version}} - <PERSON><PERSON><PERSON><PERSON>, aby w<PERSON><PERSON><PERSON><PERSON>lić informacje o wydaniu"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> Cloud już wkrótce!", "description": "Uruchamiaj zdalne agenty w chmurze, uzyskuj dostęp do swoich zadań z dowolnego miejsca, współpracuj z innymi i wiele więcej.", "joinWaitlist": "Dołącz do listy oczekujących, aby uzyskać wczesny dostęp."}, "editMessage": {"placeholder": "Ed<PERSON><PERSON><PERSON> swoją wiadom<PERSON>..."}}