{"greeting": "歡迎使用 湛盧", "task": {"title": "工作", "seeMore": "顯示更多", "seeLess": "顯示較少", "tokens": "Tokens:", "cache": "快取：", "apiCost": "API 費用：", "contextWindow": "上下文長度：", "closeAndStart": "關閉現有工作並開始一項新的工作", "export": "匯出工作紀錄", "delete": "刪除工作（按住 Shift 並點選可跳過確認）", "condenseContext": "智慧壓縮上下文", "share": "分享工作", "copyId": "複製任務ID", "shareWithOrganization": "與組織分享", "shareWithOrganizationDescription": "僅組織成員可存取", "sharePublicly": "公開分享", "sharePubliclyDescription": "任何擁有連結的人都可存取", "connectToCloud": "連接到雲端", "connectToCloudDescription": "登入 湛卢 Cloud 以分享工作", "sharingDisabledByOrganization": "組織已停用分享功能", "shareSuccessOrganization": "組織連結已複製到剪貼簿", "shareSuccessPublic": "公開連結已複製到剪貼簿"}, "unpin": "取消置頂", "pin": "置頂", "tokenProgress": {"availableSpace": "可用空間：{{amount}} tokens", "tokensUsed": "已使用 tokens: {{used}} / {{total}}", "reservedForResponse": "為模型回應保留：{{amount}} tokens"}, "retry": {"title": "重試", "tooltip": "再次嘗試操作"}, "startNewTask": {"title": "開始新工作", "tooltip": "開始一項新工作"}, "proceedAnyways": {"title": "仍要繼續", "tooltip": "在命令執行時繼續"}, "save": {"title": "儲存", "tooltip": "儲存訊息變更"}, "reject": {"title": "拒絕", "tooltip": "拒絕此操作"}, "completeSubtaskAndReturn": "完成子工作並返回", "approve": {"title": "核准", "tooltip": "核准此操作"}, "runCommand": {"title": "執行命令", "tooltip": "執行此命令"}, "proceedWhileRunning": {"title": "執行時繼續", "tooltip": "儘管有警告仍繼續執行"}, "killCommand": {"title": "終止指令", "tooltip": "終止目前的指令"}, "resumeTask": {"title": "繼續工作", "tooltip": "繼續目前的工作"}, "terminate": {"title": "終止", "tooltip": "結束目前的工作"}, "cancel": {"title": "取消", "tooltip": "取消目前操作"}, "scrollToBottom": "捲動至聊天底部", "about": "透過 湛盧研發大模型 輔助生成、修復、重構和除錯程式碼。<br />查看我們的 <DocsLink>說明文件</DocsLink> 了解更多資訊。", "onboarding": "此工作區中的任務列表為空，請在下方輸入任務開始。<br>不確定如何開始？請在 <DocsLink>說明文件</DocsLink> 中閱讀更多相關資訊。", "zhanluTips": {"boomerangTasks": {"title": "任務編排", "description": "將任務拆分為更小、更易於管理的部分。"}, "stickyModels": {"title": "黏性模式", "description": "每個模式都會記住您上次使用的模型"}, "tools": {"title": "工具", "description": "允許 AI 透過瀏覽網路、執行命令等方式解決問題。"}, "customizableModes": {"title": "自訂模式", "description": "具有專屬行為和指定模型的特定角色"}, "architect": {"title": "架構師模式", "description": "規劃系統架構並設計程式碼結構。"}, "code": {"title": "程式模式", "description": "產生、修改和最佳化程式碼。"}, "unit_test": {"title": "單元測試模式", "description": "建立全面的測試以確保程式碼穩定性。"}, "project_fix": {"title": "專案修復模式", "description": "識別和修復專案中的問題或錯誤。"}, "security_fix": {"title": "安全性修復模式", "description": "識別和解決程式碼中的安全性漏洞。"}, "code_review": {"title": "程式碼審查模式", "description": "分析程式碼品質並提供改進建議。"}, "documentation": {"title": "文件模式", "description": "建立清晰詳盡的文件和說明。"}, "qa": {"title": "問答模式", "description": "回答問題和提供簡單的協助。"}}, "selectMode": "選擇互動模式", "selectApiConfig": "選取 API 設定", "internetSearch": "開啟互聯網檢索後，您可蒐索到互聯網上相關內容", "internetSearchClosed": "關閉互聯網檢索", "enhancePrompt": "使用額外內容增強提示", "addImages": "新增圖片到訊息中", "sendMessage": "傳送訊息", "stopTts": "停止文字轉語音", "typeMessage": "輸入訊息...", "typeTask": "在此處輸入您的工作...", "addContext": "輸入 @ 新增內容，輸入 / 切換模式，輸入 # 快捷指令", "dragFiles": "按住 Shift 鍵拖曳檔案", "dragFilesImages": "按住 Shift 鍵拖曳檔案/圖片", "enhancePromptDescription": "「增強提示」按鈕透過提供額外內容、說明或重新表述來幫助改進您的請求。嘗試在此處輸入請求，然後再次點選按鈕以了解其運作方式。", "modeSelector": {"title": "模式", "marketplace": "模式市集", "settings": "模式設定", "description": "專門定制湛盧行為的角色。"}, "errorReadingFile": "讀取檔案時發生錯誤：", "noValidImages": "未處理到任何有效圖片", "separator": "分隔符號", "edit": "編輯...", "forNextMode": "用於下一個模式", "forPreviousMode": "用於上一個模式", "error": "錯誤", "warning": "警告", "diffError": {"title": "編輯失敗"}, "troubleMessage": "湛盧 遇到問題...", "apiRequest": {"title": "API 請求", "failed": "API 請求失敗", "streaming": "正在處理 API 請求...", "cancelled": "API 請求已取消", "streamingFailed": "API 串流處理失敗"}, "checkpoint": {"initial": "初始檢查點", "regular": "檢查點", "initializingWarning": "正在初始化檢查點...如果耗時過長，你可以在<settingsLink>設定</settingsLink>中停用檢查點並重新啟動任務。", "menu": {"viewDiff": "檢視差異", "restore": "還原檢查點", "restoreFiles": "還原檔案", "restoreFilesDescription": "將您的專案檔案還原到此時的快照。", "restoreFilesAndTask": "還原檔案和工作", "confirm": "確認", "cancel": "取消", "cannotUndo": "此操作無法復原。", "restoreFilesAndTaskDescription": "將您的專案檔案還原到此時的快照，並刪除此點之後的所有訊息。"}, "current": "目前"}, "instructions": {"wantsToFetch": "湛盧 想要取得詳細指示以協助目前任務"}, "fileOperations": {"wantsToRead": "湛盧 想要讀取此檔案：", "wantsToReadOutsideWorkspace": "湛盧 想要讀取此工作區外的檔案：", "didRead": "湛盧 已讀取此檔案：", "wantsToEdit": "湛盧 想要編輯此檔案：", "wantsToEditOutsideWorkspace": "湛盧 想要編輯此工作區外的檔案：", "wantsToEditProtected": "湛盧 想要編輯受保護的設定檔案：", "wantsToCreate": "湛盧 想要建立新檔案：", "wantsToSearchReplace": "湛盧 想要在此檔案中搜尋和取代：", "didSearchReplace": "湛盧 已在此檔案執行搜尋和取代：", "wantsToInsert": "湛盧 想要在此檔案中插入內容：", "wantsToInsertWithLineNumber": "湛盧 想要在此檔案第 {{lineNumber}} 行插入內容：", "wantsToInsertAtEnd": "湛盧 想要在此檔案末尾新增內容：", "wantsToReadAndXMore": "湛盧 想要讀取此檔案以及另外 {{count}} 個檔案：", "wantsToReadMultiple": "湛盧 想要讀取多個檔案：", "wantsToApplyBatchChanges": "湛盧 想要對多個檔案套用變更："}, "directoryOperations": {"wantsToViewTopLevel": "湛盧 想要檢視此目錄中最上層的檔案：", "didViewTopLevel": "湛盧 已檢視此目錄中最上層的檔案：", "wantsToViewRecursive": "湛盧 想要遞迴檢視此目錄中的所有檔案：", "didViewRecursive": "湛盧 已遞迴檢視此目錄中的所有檔案：", "wantsToViewDefinitions": "湛盧 想要檢視此目錄中使用的原始碼定義名稱：", "didViewDefinitions": "湛盧 已檢視此目錄中使用的原始碼定義名稱：", "wantsToSearch": "湛盧 想要在此目錄中搜尋 <code>{{regex}}</code>：", "didSearch": "湛盧 已在此目錄中搜尋 <code>{{regex}}</code>：", "wantsToSearchOutsideWorkspace": "湛盧 想要在此目錄（工作區外）中搜尋 <code>{{regex}}</code>：", "didSearchOutsideWorkspace": "湛盧 已在此目錄（工作區外）中搜尋 <code>{{regex}}</code>：", "wantsToViewTopLevelOutsideWorkspace": "湛盧 想要檢視此目錄（工作區外）中最上層的檔案：", "didViewTopLevelOutsideWorkspace": "湛盧 已檢視此目錄（工作區外）中最上層的檔案：", "wantsToViewRecursiveOutsideWorkspace": "湛盧 想要遞迴檢視此目錄（工作區外）中的所有檔案：", "didViewRecursiveOutsideWorkspace": "湛盧 已遞迴檢視此目錄（工作區外）中的所有檔案：", "wantsToViewDefinitionsOutsideWorkspace": "湛盧 想要檢視此目錄（工作區外）中使用的原始碼定義名稱：", "didViewDefinitionsOutsideWorkspace": "湛盧 已檢視此目錄（工作區外）中使用的原始碼定義名稱："}, "commandOutput": "命令輸出", "commandExecution": {"running": "正在執行", "pid": "PID: {{pid}}", "exited": "已退出 ({{exitCode}})", "manageCommands": "管理命令權限", "commandManagementDescription": "管理命令權限：點擊 ✓ 允許自動執行，點擊 ✗ 拒絕執行。可以開啟/關閉模式或從清單中刪除。<settingsLink>檢視所有設定</settingsLink>", "addToAllowed": "新增至允許清單", "removeFromAllowed": "從允許清單中移除", "addToDenied": "新增至拒絕清單", "removeFromDenied": "從拒絕清單中移除", "abortCommand": "中止命令執行", "expandOutput": "展開輸出", "collapseOutput": "折疊輸出", "expandManagement": "展開命令管理部分", "collapseManagement": "折疊命令管理部分"}, "response": "回應", "arguments": "參數", "mcp": {"wantsToUseTool": "湛盧 想要在 {{serverName}} MCP 伺服器上使用工具：", "wantsToAccessResource": "湛盧 想要存取 {{serverName}} MCP 伺服器上的資源："}, "modes": {"wantsToSwitch": "湛盧 想要切換至 <code>{{mode}}</code> 模式", "wantsToSwitchWithReason": "湛盧 想要切換至 <code>{{mode}}</code> 模式，原因：{{reason}}", "didSwitch": "湛盧 已切換至 <code>{{mode}}</code> 模式", "didSwitchWithReason": "湛盧 已切換至 <code>{{mode}}</code> 模式，原因：{{reason}}"}, "subtasks": {"wantsToCreate": "湛盧 想要在 <code>{{mode}}</code> 模式下建立新的子工作：", "wantsToFinish": "湛盧 想要完成此子工作", "newTaskContent": "子工作指示", "completionContent": "子工作已完成", "resultContent": "子工作結果", "defaultResult": "請繼續下一個工作。", "completionInstructions": "子工作已完成！您可以檢閱結果並提出修正或下一步建議。如果一切看起來良好，請確認以將結果傳回主工作。"}, "questions": {"hasQuestion": "湛盧 有一個問題："}, "taskCompleted": "工作完成", "powershell": {"issues": "看起來您遇到了 Windows PowerShell 的問題，請參考此處"}, "autoApprove": {"title": "自動核准：", "none": "無", "description": "自動核准讓 湛卢 可以在無需徵求您同意的情況下執行動作。請僅對您完全信任的動作啟用此功能。您可以在<settingsLink>設定</settingsLink>中進行更詳細的調整。", "selectOptionsFirst": "請至少選擇以下一個選項以啟用自動核准", "toggleAriaLabel": "切換自動核准", "disabledAriaLabel": "自動核准已停用 - 請先選取選項"}, "reasoning": {"thinking": "思考中", "seconds": "{{count}}秒"}, "contextCondense": {"title": "上下文已壓縮", "condensing": "正在壓縮上下文...", "errorHeader": "上下文壓縮失敗", "tokens": "tokens"}, "followUpSuggest": {"copyToInput": "複製到輸入框（或按住 Shift 並點選）", "autoSelectCountdown": "{{count}}秒後自動選擇", "countdownDisplay": "{{count}}秒"}, "announcement": {"title": "🎉 湛盧 2.3.2 版本更新", "description": "Bug修復，程式碼補全", "whatsNew": "重要更新", "feature1": "<bold>程式碼補全bug修復</bold>: 修復程式碼補全bug", "feature2": "<bold>模式顯示bug修復</bold>: 可顯示正在使用的模式", "feature3": "<bold>刪除用戶輸入優化</bold>: 支援全選輸入內容刪除", "feature4": "<bold>智慧問答可網際網路檢索</bold>: 智慧問答模式下支援聯網檢索資訊", "hideButton": "隱藏公告", "detailsDiscussLinks": "查看 <discordLink>詳細文檔</discordLink> 了解更多功能 🚀"}, "browser": {"rooWantsToUse": "湛盧 想要使用瀏覽器：", "consoleLogs": "主控台記錄", "noNewLogs": "（沒有新記錄）", "screenshot": "瀏覽器螢幕擷圖", "cursor": "游標", "navigation": {"step": "步驟 {{current}} / {{total}}", "previous": "上一步", "next": "下一步"}, "sessionStarted": "瀏覽器工作階段已啟動", "actions": {"title": "瀏覽器動作：", "launch": "在 {{url}} 啟動瀏覽器", "click": "點選 ({{coordinate}})", "type": "輸入「{{text}}」", "scrollDown": "向下捲動", "scrollUp": "向上捲動", "close": "關閉瀏覽器"}}, "codeblock": {"tooltips": {"expand": "展開程式碼區塊", "collapse": "摺疊程式碼區塊", "enable_wrap": "啟用自動換行", "disable_wrap": "停用自動換行", "copy_code": "複製程式碼"}}, "qucikInstructions": {"UiToCode": "UI設計圖生成程式碼", "UmlToCode": "UML圖生成程式碼", "ExplainCode": "程式碼解釋", "FixCode": "程式碼糾錯", "ImproveCode": "程式碼優化", "UnitTest": "單元測試", "CODE_REVIEW": "程式碼評審", "CommentCode": "程式碼註解", "PlusButtonClicked": "清空對話方塊"}, "systemPromptWarning": "警告：自訂系統提示詞覆蓋已啟用。這可能嚴重破壞功能並導致不可預測的行為。", "profileViolationWarning": "目前設定檔與您的組織設定不相容", "shellIntegration": {"title": "命令執行警告", "description": "您的命令正在沒有 VSCode 終端機 shell 整合的情況下執行。要隱藏此警告，您可以在 <settingsLink>Zhanlu 設定</settingsLink>的 <strong>Terminal</strong> 部分停用 shell 整合，或使用下方連結排查 VSCode 終端機整合問題。", "troubleshooting": "點擊此處查看 shell 整合文件。"}, "ask": {"autoApprovedRequestLimitReached": {"title": "已達自動核准請求限制", "description": "湛卢已達到 {{count}} 次 API 請求的自動核准限制。您想要重設計數並繼續工作嗎？", "button": "重設並繼續"}}, "codebaseSearch": {"wantsToSearch": "湛卢想要搜尋程式碼庫：<code>{{query}}</code>", "wantsToSearchWithPath": "湛卢想要在 <code>{{path}}</code> 中搜尋：<code>{{query}}</code>", "didSearch_one": "找到 1 個結果", "didSearch_other": "找到 {{count}} 個結果", "resultTooltip": "相似度評分：{{score}} (點擊開啟檔案)"}, "read-batch": {"approve": {"title": "全部核准"}, "deny": {"title": "全部拒絕"}}, "indexingStatus": {"ready": "索引就緒", "indexing": "索引中 {{percentage}}%", "indexed": "已索引", "error": "索引錯誤", "status": "索引狀態"}, "versionIndicator": {"ariaLabel": "版本 {{version}} - 點擊查看發布說明"}, "zhanluCloudCTA": {"title": "湛卢 Cloud 即將推出！", "description": "在雲端執行遠端代理，隨時隨地存取您的工作，與他人協作等更多功能。", "joinWaitlist": "加入等候名單以獲得早期存取權限。"}, "editMessage": {"placeholder": "編輯訊息..."}}