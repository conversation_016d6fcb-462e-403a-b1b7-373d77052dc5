{"greeting": "<PERSON><PERSON>o mừng đến v<PERSON><PERSON>", "task": {"title": "Nhiệm vụ", "seeMore": "<PERSON><PERSON>", "seeLess": "<PERSON><PERSON>", "tokens": "Tokens:", "cache": "Bộ nhớ đệm:", "apiCost": "Chi phí API:", "contextWindow": "<PERSON><PERSON><PERSON> dài b<PERSON>i cảnh:", "closeAndStart": "<PERSON><PERSON><PERSON> nhiệm vụ và bắt đầu nhiệm vụ mới", "export": "<PERSON><PERSON><PERSON> lịch sử nhiệm vụ", "delete": "<PERSON><PERSON><PERSON> nhi<PERSON> vụ (Shift + Click để bỏ qua xác nhận)", "condenseContext": "<PERSON><PERSON> đọng ngữ cảnh thông minh", "share": "Chia sẻ nhiệm vụ", "copyId": "Sao chép ID nhiệm vụ", "shareWithOrganization": "<PERSON>a sẻ với tổ chức", "shareWithOrganizationDescription": "Chỉ thành viên tổ chức của bạn mới có thể truy cập", "sharePublicly": "<PERSON>a sẻ công khai", "sharePubliclyDescription": "<PERSON><PERSON>t kỳ ai có liên kết đều có thể truy cập", "connectToCloud": "<PERSON><PERSON><PERSON> n<PERSON>i với <PERSON>", "connectToCloudDescription": "<PERSON><PERSON><PERSON> nhập v<PERSON><PERSON> để chia sẻ tác vụ", "sharingDisabledByOrganization": "<PERSON><PERSON> sẻ bị tổ chức vô hiệu hóa", "shareSuccessOrganization": "<PERSON><PERSON><PERSON> kết tổ chức đã đư<PERSON>c sao chép vào clipboard", "shareSuccessPublic": "<PERSON><PERSON><PERSON> kết công khai đã được sao chép vào clipboard"}, "unpin": "Bỏ ghim khỏi đầu", "pin": "<PERSON><PERSON> lên đ<PERSON>u", "tokenProgress": {"availableSpace": "<PERSON><PERSON>ông gian khả dụng: {{amount}} tokens", "tokensUsed": "Tokens đã sử dụng: {{used}} trong {{total}}", "reservedForResponse": "<PERSON><PERSON><PERSON> riêng cho phản hồi mô hình: {{amount}} tokens"}, "retry": {"title": "<PERSON><PERSON><PERSON> lại", "tooltip": "<PERSON><PERSON><PERSON> lại thao tác"}, "startNewTask": {"title": "<PERSON><PERSON><PERSON> đ<PERSON> n<PERSON> vụ mới", "tooltip": "<PERSON>ắt đầu một nhiệm vụ mới"}, "proceedAnyways": {"title": "Vẫn tiếp tục", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tục trong khi lệnh đang chạy"}, "save": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> c<PERSON>c thay đổi tin nh<PERSON>n"}, "reject": {"title": "<PERSON><PERSON> chối", "tooltip": "Từ chối hành động này"}, "completeSubtaskAndReturn": "<PERSON><PERSON><PERSON> thành nhi<PERSON> vụ phụ và quay lại", "approve": {"title": "<PERSON><PERSON>", "tooltip": "<PERSON><PERSON>t hành động này"}, "runCommand": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON>h<PERSON><PERSON> thi lệnh này"}, "proceedWhileRunning": {"title": "<PERSON><PERSON><PERSON><PERSON> tục trong khi chạy", "tooltip": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> bất chấp cảnh báo"}, "killCommand": {"title": "<PERSON><PERSON><PERSON> l<PERSON>", "tooltip": "<PERSON><PERSON><PERSON> l<PERSON>nh hiện tại"}, "resumeTask": {"title": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> vụ", "tooltip": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> vụ hiện tại"}, "terminate": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> n<PERSON> v<PERSON> hiện tại"}, "cancel": {"title": "<PERSON><PERSON><PERSON>", "tooltip": "<PERSON><PERSON><PERSON> thao tác hiện tại"}, "scrollToBottom": "<PERSON><PERSON><PERSON>n xuống cuối cuộc trò chuyện", "about": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, tái cấu trúc và gỡ lỗi mã với sự hỗ trợ của Mô hình <PERSON>lu.<br />Xem <DocsLink>tài liệu</DocsLink> của chúng tôi để biết thêm thông tin.", "onboarding": "<PERSON>h sách nhiệm vụ trong không gian làm việc này đang trống. Bắt đầu bằng cách nhập nhiệm vụ của bạn bên dưới.<br><PERSON>hông chắc cách bắt đầu? <PERSON><PERSON><PERSON> thêm trong <DocsLink>tài liệu</DocsLink> của chúng tôi.", "zhanluTips": {"boomerangTasks": {"title": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> vụ", "description": "<PERSON><PERSON> nhỏ các nhiệm vụ thành các phần nhỏ hơn, <PERSON><PERSON> quản lý hơn."}, "stickyModels": {"title": "<PERSON><PERSON> độ dính", "description": "Mỗi chế độ ghi nhớ mô hình đã sử dụng cuối cùng của bạn"}, "tools": {"title": "<PERSON><PERSON><PERSON> cụ", "description": "<PERSON> phép AI gi<PERSON>i quyết vấn đề bằng cách duyệt web, ch<PERSON><PERSON> l<PERSON>, v.v."}, "customizableModes": {"title": "Chế độ tùy chỉnh", "description": "<PERSON><PERSON>c nhân vật chuyên biệt với hành vi riêng và mô hình được chỉ định"}, "architect": {"title": "<PERSON><PERSON> độ <PERSON> trúc sư", "description": "<PERSON><PERSON><PERSON> kế hoạch kiến trúc hệ thống và thiết kế cấu trúc mã."}, "code": {"title": "<PERSON><PERSON> độ Mã nguồn", "description": "<PERSON><PERSON><PERSON>, sửa đổi và tối ưu hóa mã."}, "unit_test": {"title": "<PERSON>ế độ <PERSON>m thử đơn vị", "description": "<PERSON><PERSON><PERSON> c<PERSON>c bài kiểm tra toàn diện để đảm bảo t<PERSON>h ổn định của mã."}, "project_fix": {"title": "<PERSON><PERSON> độ Sửa dự án", "description": "<PERSON><PERSON><PERSON> định và khắc phục các vấn đề hoặc lỗi trong dự án."}, "security_fix": {"title": "<PERSON><PERSON> độ Sửa lỗi bảo mật", "description": "Nhận diện và giải quyết các lỗ hổng bảo mật trong mã."}, "code_review": {"title": "<PERSON>ế độ <PERSON>h giá mã", "description": "<PERSON><PERSON> tích chất lượng mã và đề xuất cải tiến."}, "documentation": {"title": "<PERSON><PERSON> độ Tài liệu", "description": "<PERSON><PERSON>o tài liệu và hướng dẫn rõ ràng, chi ti<PERSON>t."}, "qa": {"title": "<PERSON>ế độ Hỏi đáp", "description": "Tr<PERSON> lời câu hỏi và cung cấp trợ giúp đơn giản."}}, "selectMode": "<PERSON><PERSON><PERSON> chế độ tương tác", "selectApiConfig": "<PERSON><PERSON><PERSON> c<PERSON>u h<PERSON>nh <PERSON>", "internetSearch": "<PERSON><PERSON> <PERSON>hi t<PERSON>y xu<PERSON>t Internet đ<PERSON><PERSON><PERSON> bật, bạn có thể tìm kiếm nội dung liên quan trên Internet.", "internetSearchClosed": "Tắt Internet Retrieval", "enhancePrompt": "<PERSON><PERSON><PERSON> cao yêu cầu với ngữ cảnh bổ sung", "addImages": "<PERSON><PERSON><PERSON><PERSON> hình <PERSON>nh vào tin nh<PERSON>n", "sendMessage": "<PERSON><PERSON><PERSON> tin nh<PERSON>n", "stopTts": "<PERSON><PERSON><PERSON> chuyển văn bản thành giọng nói", "typeMessage": "<PERSON><PERSON><PERSON><PERSON> tin nhắn...", "typeTask": "<PERSON><PERSON><PERSON><PERSON> vụ của bạn tại đây...", "addContext": "@ Thêm ngữ cảnh,/chuyển đổi chế độ, # lệnh tắt", "dragFiles": "<PERSON><PERSON>ữ Shift để kéo tệp", "dragFilesImages": "Giữ Shift để kéo tệp/hình <PERSON>nh", "enhancePromptDescription": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> cao yêu cầu' gi<PERSON><PERSON> cải thiện yêu cầu của bạn bằng cách cung cấp ngữ cảnh bổ sung, làm rõ hoặc diễn đạt lại. H<PERSON><PERSON> thử nhập yêu cầu tại đây và nhấp vào nút một lần nữa để xem cách thức hoạt động.", "modeSelector": {"title": "<PERSON><PERSON> độ", "marketplace": "Chợ Chế độ", "settings": "Cài đặt Chế độ", "description": "<PERSON><PERSON><PERSON> nhân cách chuyên biệt điều chỉnh hành vi củ<PERSON>."}, "errorReadingFile": "Lỗi khi đọc tệp:", "noValidImages": "<PERSON><PERSON><PERSON><PERSON> có hình <PERSON>nh hợp lệ nào đư<PERSON><PERSON> xử lý", "separator": "<PERSON><PERSON><PERSON> phân cách", "edit": "Chỉnh sửa...", "forNextMode": "cho chế độ tiếp theo", "forPreviousMode": "cho chế độ trư<PERSON>c đó", "error": "Lỗi", "warning": "<PERSON><PERSON><PERSON> b<PERSON>o", "diffError": {"title": "Chỉnh sửa không thành công"}, "troubleMessage": "<PERSON><PERSON><PERSON> đang gặp sự cố...", "apiRequest": {"title": "<PERSON><PERSON><PERSON> c<PERSON>", "failed": "<PERSON><PERSON><PERSON> c<PERSON>u <PERSON> thất bại", "streaming": "<PERSON><PERSON><PERSON> cầu API...", "cancelled": "<PERSON><PERSON><PERSON> c<PERSON>u API đã hủy", "streamingFailed": "Streaming API thất bại"}, "checkpoint": {"initial": "<PERSON><PERSON><PERSON><PERSON> kiểm tra ban đầu", "regular": "<PERSON><PERSON><PERSON><PERSON> kiểm tra", "initializingWarning": "<PERSON>ang khởi tạo điểm kiểm tra... Nếu quá trình này mất quá nhiều thời gian, bạn có thể vô hiệu hóa điểm kiểm tra trong <settingsLink>cài đặt</settingsLink> và khởi động lại tác vụ của bạn.", "menu": {"viewDiff": "<PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON><PERSON> phục điểm kiểm tra", "restoreFiles": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> t<PERSON>p", "restoreFilesDescription": "<PERSON><PERSON><PERSON><PERSON> phục các tệp dự án của bạn về bản chụp được thực hiện tại thời điểm này.", "restoreFilesAndTask": "Khô<PERSON> phục tệ<PERSON> & nhiệm vụ", "confirm": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "cannotUndo": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "restoreFilesAndTaskDescription": "<PERSON><PERSON><PERSON><PERSON> phục các tệp dự án của bạn về bản chụp được thực hiện tại thời điểm này và xóa tất cả tin nhắn sau điểm này."}, "current": "<PERSON><PERSON><PERSON> t<PERSON>i"}, "instructions": {"wantsToFetch": "<PERSON><PERSON><PERSON> muốn lấy hướng dẫn chi tiết để hỗ trợ nhiệm vụ hiện tại"}, "fileOperations": {"wantsToRead": "<PERSON><PERSON><PERSON> muốn đ<PERSON><PERSON> tệ<PERSON> nà<PERSON>:", "wantsToReadOutsideWorkspace": "<PERSON><PERSON><PERSON> muốn đọc tệp này bên ngoài không gian làm việc:", "didRead": "<PERSON><PERSON><PERSON> đã đọc tệp này:", "wantsToEdit": "<PERSON><PERSON><PERSON> muốn chỉnh sửa tệp này:", "wantsToEditOutsideWorkspace": "<PERSON><PERSON><PERSON> muốn chỉnh sửa tệp này bên ngoài không gian làm việc:", "wantsToEditProtected": "<PERSON><PERSON><PERSON> muốn chỉnh sửa tệp cấu hình được bảo vệ:", "wantsToCreate": "<PERSON><PERSON><PERSON> muốn tạo một tệp mới:", "wantsToSearchReplace": "<PERSON><PERSON><PERSON> muốn thực hiện tìm kiếm và thay thế trong tệp này:", "didSearchReplace": "<PERSON><PERSON><PERSON> đã thực hiện tìm kiếm và thay thế trong tệp này:", "wantsToInsert": "<PERSON><PERSON><PERSON> muốn chèn nội dung vào tệp này:", "wantsToInsertWithLineNumber": "<PERSON><PERSON><PERSON> muốn chèn nội dung vào dòng {{lineNumber}} của tệp này:", "wantsToInsertAtEnd": "<PERSON><PERSON><PERSON> muốn thêm nội dung vào cuối tệp này:", "wantsToReadAndXMore": "<PERSON><PERSON><PERSON> muốn đọc tệp này và {{count}} tệp khác:", "wantsToReadMultiple": "<PERSON><PERSON><PERSON> muốn đ<PERSON><PERSON> nhi<PERSON>u tệp:", "wantsToApplyBatchChanges": "<PERSON><PERSON><PERSON> muốn áp dụng thay đổi cho nhiều tệp:"}, "directoryOperations": {"wantsToViewTopLevel": "<PERSON><PERSON><PERSON> muốn xem các tệp cấp cao nhất trong thư mục này:", "didViewTopLevel": "<PERSON><PERSON><PERSON> đã xem các tệp cấp cao nhất trong thư mục này:", "wantsToViewRecursive": "<PERSON><PERSON><PERSON> muốn xem đệ quy tất cả các tệp trong thư mục này:", "didViewRecursive": "<PERSON><PERSON><PERSON> đã xem đệ quy tất cả các tệp trong thư mục này:", "wantsToViewDefinitions": "<PERSON><PERSON><PERSON> muốn xem tên định nghĩa mã nguồn được sử dụng trong thư mục này:", "didViewDefinitions": "<PERSON><PERSON><PERSON> đã xem tên định nghĩa mã nguồn đư<PERSON><PERSON> sử dụng trong thư mục này:", "wantsToSearch": "<PERSON><PERSON><PERSON> muốn tìm kiếm trong thư mục này cho <code>{{regex}}</code>:", "didSearch": "<PERSON><PERSON><PERSON> đã tìm kiếm trong thư mục này cho <code>{{regex}}</code>:", "wantsToSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> muốn tìm kiếm trong thư mục này (ngo<PERSON>i không gian làm việc) cho <code>{{regex}}</code>:", "didSearchOutsideWorkspace": "<PERSON><PERSON><PERSON> đã tìm kiếm trong thư mục này (ngo<PERSON>i không gian làm việc) cho <code>{{regex}}</code>:", "wantsToViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> muốn xem các tệp cấp cao nhất trong thư mục này (ngo<PERSON>i không gian làm việc):", "didViewTopLevelOutsideWorkspace": "<PERSON><PERSON><PERSON> đã xem các tệp cấp cao nhất trong thư mục này (ngoài không gian làm việc):", "wantsToViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> muốn xem đệ quy tất cả các tệp trong thư mục này (ngo<PERSON>i không gian làm việc):", "didViewRecursiveOutsideWorkspace": "<PERSON><PERSON><PERSON> đã xem đệ quy tất cả các tệp trong thư mục này (ngoài không gian làm việc):", "wantsToViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> muốn xem tên định nghĩa mã nguồn được sử dụng trong thư mục này (ngoài không gian làm việc):", "didViewDefinitionsOutsideWorkspace": "<PERSON><PERSON><PERSON> đã xem tên định nghĩa mã nguồn được sử dụng trong thư mục này (ngoài không gian làm việc):"}, "commandOutput": "<PERSON><PERSON><PERSON> qu<PERSON> l<PERSON>nh", "commandExecution": {"running": "<PERSON><PERSON>", "pid": "PID: {{pid}}", "exited": "<PERSON><PERSON> thoát ({{exitCode}})", "manageCommands": "<PERSON><PERSON><PERSON><PERSON> lý quyền lệnh", "commandManagementDescription": "<PERSON><PERSON><PERSON>n lý quyền lệnh: <PERSON><PERSON><PERSON><PERSON> vào ✓ để cho phép tự động thực thi, ✗ để từ chối thực thi. <PERSON><PERSON>c mẫu có thể được bật/tắt hoặc xóa khỏi danh sách. <settingsLink>Xem tất cả cài đặt</settingsLink>", "addToAllowed": "<PERSON>h<PERSON><PERSON> vào danh sách cho phép", "removeFromAllowed": "Xóa khỏi danh sách cho phép", "addToDenied": "<PERSON><PERSON><PERSON><PERSON> vào danh sách từ chối", "removeFromDenied": "<PERSON><PERSON>a khỏi danh sách từ chối", "abortCommand": "<PERSON><PERSON>y bỏ thực thi lệnh", "expandOutput": "Mở rộng kết quả", "collapseOutput": "<PERSON><PERSON> g<PERSON>n kết quả", "expandManagement": "Mở rộng phần quản lý lệnh", "collapseManagement": "<PERSON><PERSON> gọn phần quản lý lệnh"}, "response": "<PERSON><PERSON><PERSON>", "arguments": "<PERSON>ham s<PERSON>", "mcp": {"wantsToUseTool": "<PERSON><PERSON><PERSON> muốn sử dụng một công cụ trên máy chủ MCP {{serverName}}:", "wantsToAccessResource": "<PERSON><PERSON><PERSON> muốn truy cập một tài nguyên trên máy chủ MCP {{serverName}}:"}, "modes": {"wantsToSwitch": "<PERSON><PERSON><PERSON> muốn chuyển sang chế độ <code>{{mode}}</code>", "wantsToSwitchWithReason": "<PERSON><PERSON><PERSON> muốn chuyển sang chế độ <code>{{mode}}</code> vì: {{reason}}", "didSwitch": "<PERSON><PERSON><PERSON> đã chuyển sang chế độ <code>{{mode}}</code>", "didSwitchWithReason": "<PERSON><PERSON><PERSON> đã chuyển sang chế độ <code>{{mode}}</code> vì: {{reason}}"}, "subtasks": {"wantsToCreate": "<PERSON><PERSON><PERSON> muốn tạo một nhiệm vụ phụ mới trong chế độ <code>{{mode}}</code>:", "wantsToFinish": "<PERSON><PERSON><PERSON> muốn hoàn thành nhiệm vụ phụ này", "newTaskContent": "Hướng dẫn nhiệm vụ phụ", "completionContent": "Nhiệm vụ phụ đã hoàn thành", "resultContent": "<PERSON><PERSON><PERSON> qu<PERSON> n<PERSON> vụ phụ", "defaultResult": "<PERSON>ui lòng tiếp tục với nhiệm vụ tiếp theo.", "completionInstructions": "Nhiệm vụ phụ đã hoàn thành! Bạn có thể xem lại kết quả và đề xuất các sửa đổi hoặc bước tiếp theo. Nếu mọi thứ có vẻ tốt, hãy xác nhận để trả kết quả về nhiệm vụ chính."}, "questions": {"hasQuestion": "<PERSON><PERSON><PERSON> có một câu hỏi:"}, "taskCompleted": "Nhiệm v<PERSON> hoàn thành", "powershell": {"issues": "<PERSON><PERSON> vẻ như bạn đang gặp vấn đề với Windows PowerShell, vui lòng xem"}, "autoApprove": {"title": "Tự động phê duyệt:", "none": "K<PERSON>ô<PERSON>", "description": "Tự động phê duyệt cho phép zhanlu thực hiện hành động mà không cần xin phép. Chỉ bật cho các hành động bạn hoàn toàn tin tưởng. Cấu hình chi tiết hơn có sẵn trong <settingsLink>Cài đặt</settingsLink>.", "selectOptionsFirst": "<PERSON>ọn ít nhất một tùy chọn bên dưới để bật tự động phê duyệt", "toggleAriaLabel": "Chuyển đổi tự động phê duyệt", "disabledAriaLabel": "Tự động phê duyệt bị vô hiệu hóa - hãy chọn các tùy chọn trước"}, "reasoning": {"thinking": "<PERSON><PERSON> suy nghĩ", "seconds": "{{count}} gi<PERSON>y"}, "contextCondense": {"title": "<PERSON><PERSON> cảnh đã tóm tắt", "condensing": "<PERSON><PERSON> cô đọng ngữ cảnh...", "errorHeader": "<PERSON><PERSON><PERSON><PERSON> thể cô đọng ngữ cảnh", "tokens": "token"}, "followUpSuggest": {"copyToInput": "<PERSON>o chép vào ô nhập liệu (hoặc Shift + nhấp chuột)", "autoSelectCountdown": "Tự động chọn sau {{count}}s", "countdownDisplay": "{{count}}s"}, "announcement": {"title": "🎉 <PERSON><PERSON><PERSON> nh<PERSON>t p<PERSON>ên bả<PERSON> 2.3.2", "description": "<PERSON><PERSON><PERSON> lỗi, tự động hoàn thành mã", "whatsNew": "<PERSON><PERSON><PERSON> nhật quan trọng", "feature1": "<bold>Thê<PERSON> chế độ Nền tảng <PERSON></bold>: Ch<PERSON> độ Nền tảng Mã tạo ra các bài tập lập trình", "feature2": "<bold>Sửa lỗi nhiệm vụ lịch sử</bold>: Vấn đề đã được giải quyết khi các nhiệm vụ mới xuất hiện trong nhiệm vụ lịch sử", "feature3": "<bold>S<PERSON>a lỗi nhấp nháy</bold>: Vấn đề nhấp nháy màn hình thỉnh thoảng khi hiển thị mã đã được giải quyết", "feature4": "<bold><PERSON><PERSON><PERSON> tối ưu hóa khác</bold>: <PERSON><PERSON><PERSON> ưu hóa các vấn đề khác", "hideButton": "Ẩn thông báo", "detailsDiscussLinks": "Xem <discordLink>tài liệu chi tiết</discordLink> để tìm hiểu thêm tính năng 🚀"}, "browser": {"rooWantsToUse": "<PERSON><PERSON><PERSON> muốn sử dụng trình duy<PERSON>:", "consoleLogs": "<PERSON><PERSON><PERSON><PERSON> ký bảng điều khiển", "noNewLogs": "(<PERSON><PERSON><PERSON><PERSON> có nhật ký mới)", "screenshot": "Ảnh chụp màn hình trình du<PERSON>t", "cursor": "con trỏ", "navigation": {"step": "Bước {{current}} / {{total}}", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON>"}, "sessionStarted": "<PERSON><PERSON><PERSON> trình duyệt đã bắt đầu", "actions": {"title": "<PERSON><PERSON><PERSON> động trình du<PERSON>: ", "launch": "Khởi chạy trình du<PERSON>t tại {{url}}", "click": "<PERSON><PERSON><PERSON>p ({{coordinate}})", "type": "G<PERSON> \"{{text}}\"", "scrollDown": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>ng", "scrollUp": "<PERSON><PERSON><PERSON><PERSON> lên", "close": "<PERSON><PERSON><PERSON> tr<PERSON>"}}, "codeblock": {"tooltips": {"expand": "Mở rộng khối mã", "collapse": "<PERSON><PERSON> gọn kh<PERSON>i mã", "enable_wrap": "<PERSON><PERSON>t tự động xuống dòng", "disable_wrap": "Tắt tự động xuống dòng", "copy_code": "Sao chép mã"}}, "qucikInstructions": {"UiToCode": "<PERSON>ã thiết kế UI Tạo mã", "UmlToCode": "Mã tạo biểu đồ UML", "ExplainCode": "Giải thích code", "FixCode": "Sửa lỗi code", "ImproveCode": "<PERSON><PERSON><PERSON>u hóa mã", "UnitTest": "<PERSON><PERSON><PERSON> tra đơn vị", "CODE_REVIEW": "Đánh giá code", "CommentCode": "Bình luận code", "PlusButtonClicked": "<PERSON><PERSON><PERSON> tho<PERSON> tr<PERSON>ng"}, "systemPromptWarning": "CẢNH BÁO: <PERSON><PERSON> kích hoạt ghi đè lệnh nhắc hệ thống tùy chỉnh. Điều này có thể phá vỡ nghiêm trọng chức năng và gây ra hành vi không thể dự đoán.", "profileViolationWarning": "<PERSON><PERSON> sơ hiện tại không tương thích với cài đặt của tổ chức của bạn", "shellIntegration": {"title": "<PERSON><PERSON><PERSON> b<PERSON>o thực thi lệnh", "description": "Lệnh của bạn đang được thực thi mà không có tích hợp shell terminal VSCode. Đ<PERSON> <PERSON>n cảnh báo nà<PERSON>, bạn có thể vô hiệu hóa tích hợp shell trong phần <strong>Terminal</strong> của <settingsLink>cài đặt Zhanlu</settingsLink> hoặc khắc phục sự cố tích hợp terminal VSCode bằng liên kết bên dưới.", "troubleshooting": "<PERSON><PERSON>ấ<PERSON> vào đây để xem tài liệu tích hợp shell."}, "ask": {"autoApprovedRequestLimitReached": {"title": "Đ<PERSON> Đ<PERSON>t G<PERSON>ới Hạn <PERSON> Tự Động <PERSON> Duy<PERSON>", "description": "zhan<PERSON> đã đạt đến giới hạn tự động phê duyệt là {{count}} yêu cầu API. Bạn có muốn đặt lại bộ đếm và tiếp tục nhiệm vụ không?", "button": "Đặt lại và T<PERSON>ế<PERSON> tục"}}, "codebaseSearch": {"wantsToSearch": "<PERSON><PERSON><PERSON> muốn tìm kiếm trong cơ sở mã cho <code>{{query}}</code>:", "wantsToSearchWithPath": "<PERSON><PERSON><PERSON> muốn tìm kiếm trong cơ sở mã cho <code>{{query}}</code> trong <code>{{path}}</code>:", "didSearch_one": "Đ<PERSON> tìm thấy 1 kết quả", "didSearch_other": "<PERSON><PERSON> tìm thấy {{count}} kết quả", "resultTooltip": "<PERSON><PERSON><PERSON><PERSON> tương tự: {{score}} (nhấp để mở tệp)"}, "read-batch": {"approve": {"title": "<PERSON><PERSON><PERSON> nhận tất cả"}, "deny": {"title": "<PERSON>ừ chối tất cả"}}, "indexingStatus": {"ready": "Chỉ mục sẵn sàng", "indexing": "<PERSON><PERSON> lập chỉ mục {{percentage}}%", "indexed": "<PERSON><PERSON> lập chỉ mục", "error": "Lỗi chỉ mục", "status": "Tr<PERSON>ng thái chỉ mục"}, "versionIndicator": {"ariaLabel": "<PERSON><PERSON><PERSON> bản {{version}} - <PERSON><PERSON><PERSON><PERSON> để xem ghi chú phát hành"}, "zhanluCloudCTA": {"title": "<PERSON><PERSON><PERSON> <PERSON> sắp ra mắt!", "description": "Chạy các agent từ xa trên cloud, t<PERSON>y cập các tác vụ của bạn từ mọi nơi, cộng tác với người khác và nhiều hơn nữa.", "joinWaitlist": "<PERSON>ham gia danh sách chờ để đượ<PERSON> truy cập sớm."}, "editMessage": {"placeholder": "Chỉnh sửa tin nhắn của bạn..."}}