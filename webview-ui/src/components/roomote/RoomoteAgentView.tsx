import React, { useState, useCallback, useMemo } from "react"
import { VSCodeButton, VSCodeTextArea } from "@vscode/webview-ui-toolkit/react"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "../../utils/vscode"
import { Tab, <PERSON>b<PERSON>onte<PERSON>, TabHeader } from "../common/Tab"
import { JiraConnection } from "./JiraConnection"
import { GitLabConnection } from "./GitLabConnection"
import { JiraConfig, JiraIssue, GitLabConfig, GitLabProject, GitLabBranch } from "@roo-code/types"
import { SelectDropdown, DropdownOptionType } from "@/components/ui/select-dropdown"
import { Loader2, GitBranch } from "lucide-react"
import { Mode, getAllModes } from "@roo/modes"
import { useExtensionState } from "@src/context/ExtensionStateContext"
import { But<PERSON> } from "@src/components/ui"
import { RemoteAgentChatView } from "./RoomoteAgentChatView"

// 临时定义 RoomoteTaskPayload 类型（与后端共享定义保持同步）
interface RoomoteTaskPayload {
	type: "custom" | "jira.gitlab"
	customInstructions?: string
	jira?: {
		config: JiraConfig
		issue: JiraIssue
	}
	gitlab: {
		config: GitLabConfig
		project: GitLabProject
		branch: string
	}
	profileName?: string
	mode?: string
}

type RoomoteAgentViewProps = {
	onDone: () => void
	listApiConfigMeta?: { id: string; name: string }[]
	currentSection?: string // 添加currentSection支持
}

export const RoomoteAgentView = ({ onDone, listApiConfigMeta, currentSection }: RoomoteAgentViewProps) => {
	const { t } = useAppTranslation()
	const { customModes, mode, setMode, currentApiConfigName, setCurrentApiConfigName } = useExtensionState()

	// 检查是否为Remote Agent聊天模式
	const isRemoteChatMode = currentSection?.startsWith("remote-chat:")
	const remoteChatInfo = useMemo(() => {
		if (!isRemoteChatMode || !currentSection) return null

		console.log("[RoomoteAgentView] Parsing currentSection:", currentSection)

		try {
			// 修复解析逻辑：使用更精确的方式分割字符串
			// 格式: remote-chat:taskId:apiUrl:historyItemJson
			const prefixPattern = /^remote-chat:([^:]+):(https?:\/\/[^:]+:[^:]+):(.+)$/
			const match = currentSection.match(prefixPattern)

			if (match) {
				const taskId = match[1]
				const apiUrl = match[2]
				const historyItemJson = match[3]
				console.log("[RoomoteAgentView] Regex match found:", {
					taskId,
					apiUrl,
					historyItemJson: historyItemJson.substring(0, 100) + "...",
				})
				const historyItem = JSON.parse(historyItemJson)
				console.log("[RoomoteAgentView] Successfully parsed remoteChatInfo:", { taskId, apiUrl, historyItem })
				return { taskId, apiUrl, historyItem }
			}

			// 如果上面的正则不匹配，尝试更通用的方式
			// 假设taskId不包含冒号，找到第二个冒号后的位置开始查找JSON
			console.log("[RoomoteAgentView] Regex failed, trying fallback parsing...")
			const parts = currentSection.split(":")
			if (parts.length >= 4) {
				const taskId = parts[1]
				// 找到JSON开始的位置（倒数第一个完整的JSON对象）
				const remainingParts = parts.slice(2)
				let apiUrl = ""
				let historyItemJson = ""

				// 从后往前尝试解析JSON，确定API URL和JSON的分界点
				for (let i = 1; i < remainingParts.length; i++) {
					try {
						const potentialJson = remainingParts.slice(i).join(":")
						JSON.parse(potentialJson) // 尝试解析JSON
						apiUrl = remainingParts.slice(0, i).join(":")
						historyItemJson = potentialJson
						console.log("[RoomoteAgentView] Fallback parsing succeeded:", {
							taskId,
							apiUrl,
							historyItemJson: historyItemJson.substring(0, 100) + "...",
						})
						break
					} catch {
						// 继续尝试下一个分割点
						continue
					}
				}

				if (apiUrl && historyItemJson) {
					const historyItem = JSON.parse(historyItemJson)
					console.log("[RoomoteAgentView] Fallback successfully parsed remoteChatInfo:", {
						taskId,
						apiUrl,
						historyItem,
					})
					return { taskId, apiUrl, historyItem }
				}
			}

			console.warn("[RoomoteAgentView] Failed to parse currentSection with all methods")
		} catch (error) {
			console.error("Failed to parse remote chat info:", error)
			console.error("currentSection:", currentSection)
		}
		return null
	}, [currentSection, isRemoteChatMode])

	// 聊天界面状态管理
	const [showChatView, setShowChatView] = useState(false)
	const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
	const [roomoteApiUrl] = useState("http://localhost:3001") // TODO: 从配置读取

	// 从Secret存储加载配置状态
	const [configsLoaded, setConfigsLoaded] = useState(false)

	// 连接状态
	const [showJiraForm, setShowJiraForm] = useState(false)
	const [showGitlabForm, setShowGitlabForm] = useState(false)

	// JIRA 连接状态
	const [jiraConfig, setJiraConfig] = useState<JiraConfig | null>(null)
	const [jiraIssues, setJiraIssues] = useState<JiraIssue[]>([])
	const [selectedJiraIssue, setSelectedJiraIssue] = useState<JiraIssue | null>(null)
	// 添加JIRA验证状态
	const [jiraVerified, setJiraVerified] = useState(false)
	const [jiraVerifying, setJiraVerifying] = useState(false)

	// GitLab 连接状态
	const [gitlabConfig, setGitlabConfig] = useState<GitLabConfig | null>(null)
	const [gitlabProjects, setGitlabProjects] = useState<GitLabProject[]>([])
	const [selectedProject, setSelectedProject] = useState<GitLabProject | null>(null)
	const [gitlabBranches, setGitlabBranches] = useState<GitLabBranch[]>([])
	const [selectedBranch, setSelectedBranch] = useState<string | null>(null)
	const [isLoadingProjects, setIsLoadingProjects] = useState(false)
	const [isLoadingBranches, setIsLoadingBranches] = useState(false)
	const [gitlabError, setGitlabError] = useState<string | null>(null)
	// 添加GitLab验证状态
	const [gitlabVerified, setGitlabVerified] = useState(false)

	// 自定义指令、模式和 AI 设置
	const [customInstructions, setCustomInstructions] = useState("")
	// 使用全局状态而不是本地状态，确保与ChatView同步
	const [isCreatingTask, setIsCreatingTask] = useState(false)

	// Profile选项
	const profileOptions = useMemo(() => {
		return (listApiConfigMeta || []).map((config) => ({
			value: config.name,
			label: config.name,
			type: DropdownOptionType.ITEM,
		}))
	}, [listApiConfigMeta])

	// 模式选项
	const modeOptions = useMemo(() => {
		return getAllModes(customModes).map((mode) => ({
			value: mode.slug,
			label: mode.name,
			type: DropdownOptionType.ITEM,
		}))
	}, [customModes])

	// 处理JIRA连接变化
	const handleJiraConfigChange = useCallback((config: JiraConfig | null) => {
		setJiraConfig(config)
		if (config) {
			// 连接成功后更新状态
			setShowJiraForm(false)
			setJiraVerified(true) // 新连接默认为已验证
		} else {
			// 断开连接，清除相关状态
			setShowJiraForm(false)
			setJiraIssues([])
			setSelectedJiraIssue(null)
			setJiraVerified(false)
			setJiraVerifying(false)
		}
	}, [])

	// 处理GitLab连接变化
	const handleGitlabConfigChange = useCallback((config: GitLabConfig | null) => {
		setGitlabConfig(config)
		if (config) {
			// 连接成功，获取项目列表
			setGitlabError(null)
			setIsLoadingProjects(true)
			setShowGitlabForm(false)
			setGitlabVerified(true) // 新连接默认为已验证
			vscode.postMessage({
				type: "gitlabGetProjects",
			})
		} else {
			// 断开连接，清除相关状态
			setShowGitlabForm(false)
			setGitlabProjects([])
			setSelectedProject(null)
			setGitlabBranches([])
			setSelectedBranch(null)
			setGitlabError(null)
			setGitlabVerified(false)
		}
	}, [])

	// 处理项目选择
	const handleProjectSelect = useCallback(
		(projectId: string) => {
			const project = gitlabProjects.find((p) => p.id.toString() === projectId)
			if (project) {
				setSelectedProject(project)
				setGitlabBranches([])
				setSelectedBranch(null)

				// 获取分支列表
				setIsLoadingBranches(true)
				vscode.postMessage({
					type: "gitlabGetBranches",
					projectId: project.id,
				})
			}
		},
		[gitlabProjects],
	)

	// 处理分支选择
	const handleBranchSelect = useCallback((branchName: string) => {
		setSelectedBranch(branchName)
	}, [])

	// 初始化profile选择 - 不需要了，直接使用全局状态

	// 初始化时从Secret存储加载配置
	React.useEffect(() => {
		// 请求从Secret存储加载配置
		vscode.postMessage({
			type: "loadRoomoteConfigs",
		})
	}, [])

	// 监听后端消息
	React.useEffect(() => {
		const handleMessage = (event: MessageEvent) => {
			const message = event.data

			switch (message.type) {
				case "roomoteConfigsLoaded":
					// 从Secret存储加载配置成功
					if (message.payload) {
						const { jiraConfig: savedJiraConfig, gitlabConfig: savedGitlabConfig } = message.payload
						if (savedJiraConfig) {
							setJiraConfig(savedJiraConfig)
							// 从存储加载的配置需要重新验证
							setJiraVerified(false)
						}
						if (savedGitlabConfig) {
							setGitlabConfig(savedGitlabConfig)
							// 从存储加载的配置需要重新验证
							setGitlabVerified(false)
						}
					}
					setConfigsLoaded(true)
					break

				case "roomoteTaskCreated":
					// 任务创建成功，跳转到聊天界面
					if (message.payload && message.payload.success) {
						setCurrentTaskId(message.payload.taskId)
						setShowChatView(true)
						setIsCreatingTask(false)

						// 同步任务到历史记录，包含配置信息
						vscode.postMessage({
							type: "syncRemoteTask",
							taskId: message.payload.taskId,
							roomoteApiUrl: roomoteApiUrl,
							remoteConfig: {
								mode: mode,
								profileName: currentApiConfigName || undefined,
								jiraIssue: selectedJiraIssue?.key,
								gitlabProject: selectedProject?.path_with_namespace,
								gitlabBranch: selectedBranch ?? undefined,
							},
						})
					} else {
						// 任务创建失败
						console.error("Failed to create Roomote task:", message.payload?.error)
						setIsCreatingTask(false)
					}
					break

				case "jiraConnectionResult":
					if (message.payload.success) {
						// JIRA 连接成功后，自动获取工单
						setJiraVerifying(true)
						vscode.postMessage({
							type: "jiraGetIssues",
						})
					} else {
						setJiraVerified(false)
						setJiraVerifying(false)
					}
					break

				case "jiraIssuesResult":
					setJiraVerifying(false)
					if (message.payload.success) {
						const issues = message.payload.issues || []
						setJiraIssues(issues)
						setJiraVerified(true) // 验证成功
					} else {
						setJiraIssues([])
						setJiraVerified(false) // 验证失败
						// 验证失败时清除配置
						setJiraConfig(null)
						// 删除存储的配置
						vscode.postMessage({
							type: "deleteJiraConfig",
						})
					}
					break

				case "gitlabConnectionResult":
					if (message.payload.success) {
						// GitLab 连接成功，获取项目列表
						setIsLoadingProjects(true)
						vscode.postMessage({
							type: "gitlabGetProjects",
						})
					} else {
						setGitlabVerified(false)
						setIsLoadingProjects(false)
					}
					break

				case "gitlabProjectsResult":
					setIsLoadingProjects(false)
					if (message.payload.success) {
						setGitlabProjects(message.payload.projects || [])
						setGitlabError(null)
						setGitlabVerified(true) // 验证成功
					} else {
						const errorMsg = message.payload.error || "未知错误"
						setGitlabError(errorMsg)
						setGitlabProjects([])
						setGitlabVerified(false) // 验证失败
						// 验证失败时清除配置
						setGitlabConfig(null)
						// 删除存储的配置
						vscode.postMessage({
							type: "deleteGitlabConfig",
						})
					}
					break

				case "gitlabBranchesResult":
					setIsLoadingBranches(false)
					if (message.payload.success) {
						const branchList = message.payload.branches || []
						setGitlabBranches(branchList)

						// 自动选择默认分支
						const defaultBranch =
							branchList.find((b: GitLabBranch) => b.default) ||
							branchList.find((b: GitLabBranch) => b.name === selectedProject?.default_branch)
						if (defaultBranch) {
							setSelectedBranch(defaultBranch.name)
						}
					} else {
						setGitlabBranches([])
					}
					break
			}
		}

		window.addEventListener("message", handleMessage)
		return () => window.removeEventListener("message", handleMessage)
	}, [selectedProject, roomoteApiUrl, selectedBranch, selectedJiraIssue?.key, mode, currentApiConfigName])

	// JIRA工单选项 - 将所有hooks移到任何条件返回之前
	const jiraIssueOptions = useMemo(() => {
		return jiraIssues.map((issue) => ({
			value: issue.key,
			label: `${issue.key}: ${issue.summary}`,
			type: DropdownOptionType.ITEM,
		}))
	}, [jiraIssues])

	// GitLab项目选项
	const gitlabProjectOptions = useMemo(() => {
		return gitlabProjects.map((project) => ({
			value: project.id.toString(),
			label: project.path_with_namespace,
			type: DropdownOptionType.ITEM,
		}))
	}, [gitlabProjects])

	// GitLab分支选项
	const gitlabBranchOptions = useMemo(() => {
		return gitlabBranches.map((branch) => ({
			value: branch.name,
			label: `${branch.name}${branch.default ? " (默认)" : ""}`,
			type: DropdownOptionType.ITEM,
		}))
	}, [gitlabBranches])

	// 创建任务
	const handleCreateTask = useCallback(async () => {
		// 验证必要条件
		if (!gitlabConfig || !selectedProject || !selectedBranch) {
			return
		}

		// 如果没有 JIRA 连接，则必须有自定义指令
		if (!jiraConfig && !customInstructions.trim()) {
			return
		}

		setIsCreatingTask(true)

		try {
			const isJiraTask = jiraConfig && selectedJiraIssue
			const taskPayload: RoomoteTaskPayload = {
				type: isJiraTask ? "jira.gitlab" : "custom",
				customInstructions: customInstructions.trim() || undefined,
				jira: isJiraTask
					? {
							config: jiraConfig,
							issue: selectedJiraIssue,
						}
					: undefined,
				gitlab: {
					config: gitlabConfig,
					project: selectedProject,
					branch: selectedBranch,
				},
				profileName: currentApiConfigName || undefined,
				mode: mode,
			}

			// 发送创建任务消息到扩展
			vscode.postMessage({
				type: "roomoteCreateTask",
				payload: taskPayload,
			})

			// 不立即关闭视图，等待任务创建成功的响应
		} catch (error) {
			console.error("Failed to create Roomote task:", error)
			setIsCreatingTask(false)
		}
	}, [
		jiraConfig,
		selectedJiraIssue,
		gitlabConfig,
		selectedProject,
		selectedBranch,
		customInstructions,
		currentApiConfigName,
		mode,
	])

	// 验证是否可以创建任务
	const canCreateTask = useMemo(() => {
		const hasGitLab = gitlabConfig && gitlabVerified && selectedProject && selectedBranch
		const hasInstructions = (jiraConfig && jiraVerified) || customInstructions.trim()
		return hasGitLab && hasInstructions
	}, [gitlabConfig, gitlabVerified, selectedProject, selectedBranch, jiraConfig, jiraVerified, customInstructions])

	// 如果是Remote Agent聊天模式，直接显示聊天界面
	if (isRemoteChatMode && remoteChatInfo) {
		return (
			<RemoteAgentChatView
				taskId={remoteChatInfo.taskId}
				roomoteApiUrl={remoteChatInfo.apiUrl}
				historyItem={remoteChatInfo.historyItem}
				onBack={() => {
					// 返回时清除section，回到主界面
					vscode.postMessage({ type: "switchTab", tab: "roomote" })
				}}
				onClose={onDone}
			/>
		)
	}

	// 如果显示聊天界面，渲染 RemoteAgentChatView
	if (showChatView && currentTaskId) {
		return (
			<RemoteAgentChatView
				taskId={currentTaskId}
				roomoteApiUrl={roomoteApiUrl}
				onBack={() => {
					setShowChatView(false)
					setCurrentTaskId(null)
				}}
				onClose={onDone}
			/>
		)
	}

	// 显示加载状态，直到配置加载完成
	if (!configsLoaded) {
		return (
			<Tab>
				<TabHeader className="flex justify-between items-center">
					<h3 className="text-vscode-foreground m-0">{t("roomote:title")}</h3>
					<Button onClick={onDone}>{t("settings:common.done")}</Button>
				</TabHeader>
				<TabContent>
					<div className="flex items-center justify-center p-8">
						<div className="flex items-center gap-2">
							<Loader2 className="w-4 h-4 animate-spin" />
							<span className="text-vscode-descriptionForeground">加载配置中...</span>
						</div>
					</div>
				</TabContent>
			</Tab>
		)
	}

	return (
		<Tab>
			<TabHeader className="flex justify-between items-center">
				<h3 className="text-vscode-foreground m-0">{t("roomote:title")}</h3>
				<Button onClick={onDone}>{t("settings:common.done")}</Button>
			</TabHeader>

			<TabContent>
				<div
					style={{
						color: "var(--vscode-foreground)",
						fontSize: "13px",
						marginBottom: "15px",
						marginTop: "5px",
					}}>
					{t("roomote:description")}
				</div>

				<div className="space-y-4">
					{/* 连接区域 - 响应式布局 */}
					<div className="flex flex-col gap-3">
						{/* JIRA 连接卡片 */}
						<div className="group relative">
							<div className="absolute -inset-0.5 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg opacity-0 group-hover:opacity-50 transition-opacity duration-300 blur"></div>
							<div className="relative bg-vscode-sideBar-background rounded-lg p-3 border border-vscode-input-border hover:border-vscode-input-activeBorder transition-colors">
								<div className="flex items-start sm:items-center gap-3 mb-2">
									<div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center shrink-0">
										<span className="codicon codicon-issues text-white text-sm sm:text-base"></span>
									</div>
									<div className="flex-1 min-w-0">
										<h3 className="text-sm font-medium text-vscode-foreground leading-tight">
											{t("roomote:jira.title")}
										</h3>
										<p className="text-xs text-vscode-descriptionForeground mt-0.5 leading-relaxed">
											{t("roomote:jira.description")}
										</p>
									</div>
								</div>

								{!jiraConfig || !jiraVerified ? (
									<>
										{!showJiraForm ? (
											<>
												{jiraVerifying && (
													<div className="flex items-center gap-2 text-sm text-vscode-descriptionForeground mb-2">
														<Loader2 className="w-4 h-4 animate-spin" />
														验证JIRA连接中...
													</div>
												)}
												<VSCodeButton
													appearance="secondary"
													onClick={() => setShowJiraForm(true)}
													className="w-full"
													disabled={jiraVerifying}>
													{t("roomote:jira.connect")}
												</VSCodeButton>
											</>
										) : (
											<JiraConnection
												onConfigChange={(config) => {
													// 保存到Secret存储
													if (config) {
														vscode.postMessage({
															type: "saveJiraConfig",
															config: config,
														})
													} else {
														vscode.postMessage({
															type: "deleteJiraConfig",
														})
													}
													handleJiraConfigChange(config)
												}}
												onIssuesFetched={() => {}}
												onCancel={() => setShowJiraForm(false)}
												initialConfig={jiraConfig}
											/>
										)}
									</>
								) : (
									<div className="space-y-3">
										<div className="flex items-center gap-2 text-green-400">
											<span className="codicon codicon-check"></span>
											<span className="text-sm font-medium">{t("roomote:jira.connected")}</span>
											<span className="text-xs text-vscode-descriptionForeground hidden sm:inline">
												已保存 ({jiraIssues.length} 个工单)
											</span>
										</div>

										{/* JIRA工单选择 */}
										<div className="space-y-2">
											{jiraIssues.length > 0 ? (
												<>
													<div className="flex items-center gap-2">
														<div className="flex-1 min-w-0">
															<SelectDropdown
																value={selectedJiraIssue?.key || ""}
																onChange={(issueKey) => {
																	const issue = jiraIssues.find(
																		(i) => i.key === issueKey,
																	)
																	setSelectedJiraIssue(issue || null)
																}}
																placeholder="搜索并选择工单..."
																title="选择JIRA工单"
																options={jiraIssueOptions}
																renderItem={(option) => (
																	<div className="flex flex-col">
																		<span className="font-medium truncate">
																			{option.label}
																		</span>
																		{(() => {
																			const issue = jiraIssues.find(
																				(i) => i.key === option.value,
																			)
																			return (
																				issue && (
																					<span className="text-xs text-vscode-descriptionForeground truncate">
																						状态: {issue.status.name} |
																						优先级: {issue.priority.name}
																					</span>
																				)
																			)
																		})()}
																	</div>
																)}
															/>
														</div>
														<VSCodeButton
															appearance="icon"
															onClick={() => {
																vscode.postMessage({
																	type: "jiraGetIssues",
																})
															}}
															className="shrink-0">
															<span className="codicon codicon-refresh"></span>
														</VSCodeButton>
													</div>

													{selectedJiraIssue && (
														<div className="text-xs text-vscode-descriptionForeground">
															已选择:{" "}
															<span className="font-medium">{selectedJiraIssue.key}</span>
														</div>
													)}
												</>
											) : (
												<div className="space-y-3">
													<div className="flex items-start gap-2">
														<div className="flex-1 text-center py-4 space-y-2 sm:space-y-3">
															<div className="text-sm text-vscode-descriptionForeground">
																暂无分配给您的工单
															</div>
														</div>
														<VSCodeButton
															appearance="icon"
															onClick={() => {
																vscode.postMessage({
																	type: "jiraGetIssues",
																})
															}}
															className="shrink-0">
															<span className="codicon codicon-refresh"></span>
														</VSCodeButton>
													</div>
												</div>
											)}
										</div>

										<VSCodeButton
											appearance="secondary"
											onClick={() => {
												vscode.postMessage({
													type: "deleteJiraConfig",
												})
												setJiraConfig(null)
												setJiraIssues([])
												setSelectedJiraIssue(null)
												setJiraVerified(false)
												setJiraVerifying(false)
											}}
											className="w-full">
											{t("roomote:jira.disconnect")}
										</VSCodeButton>
									</div>
								)}
							</div>
						</div>

						{/* GitLab 连接卡片 */}
						<div className="group relative">
							<div className="absolute -inset-0.5 bg-gradient-to-r from-orange-600 to-red-600 rounded-lg opacity-0 group-hover:opacity-50 transition-opacity duration-300 blur"></div>
							<div className="relative bg-vscode-sideBar-background rounded-lg p-3 border border-vscode-input-border hover:border-vscode-input-activeBorder transition-colors">
								<div className="flex items-start sm:items-center gap-3 mb-2">
									<div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-orange-500 to-red-500 rounded-lg flex items-center justify-center shrink-0">
										<span className="codicon codicon-source-control text-white text-sm sm:text-base"></span>
									</div>
									<div className="flex-1 min-w-0">
										<h3 className="text-sm font-medium text-vscode-foreground flex items-center gap-1 leading-tight">
											{t("roomote:gitlab.title")}
											<span className="text-vscode-errorForeground text-sm">*</span>
										</h3>
										<p className="text-xs text-vscode-descriptionForeground mt-0.5 leading-relaxed">
											{t("roomote:gitlab.description")}
										</p>
									</div>
								</div>

								{!gitlabConfig || !gitlabVerified ? (
									<>
										{!showGitlabForm ? (
											<VSCodeButton
												appearance="primary"
												onClick={() => setShowGitlabForm(true)}
												className="w-full">
												{t("roomote:gitlab.connect")}
											</VSCodeButton>
										) : (
											<GitLabConnection
												onConfigChange={(config) => {
													// 保存到Secret存储
													if (config) {
														vscode.postMessage({
															type: "saveGitlabConfig",
															config: config,
														})
													} else {
														vscode.postMessage({
															type: "deleteGitlabConfig",
														})
													}
													handleGitlabConfigChange(config)
												}}
												onProjectChange={setSelectedProject}
												onBranchChange={setSelectedBranch}
												onCancel={() => setShowGitlabForm(false)}
												initialConfig={gitlabConfig}
											/>
										)}
									</>
								) : (
									<div className="space-y-3">
										<div className="flex items-center gap-2 text-green-400">
											<span className="codicon codicon-check"></span>
											<span className="text-sm font-medium">{t("roomote:gitlab.connected")}</span>
											<span className="text-xs text-vscode-descriptionForeground hidden sm:inline">
												已保存
											</span>
										</div>

										{/* 项目选择 */}
										<div className="space-y-2">
											{isLoadingProjects ? (
												<div className="flex items-center gap-2 text-sm text-vscode-descriptionForeground">
													<Loader2 className="w-4 h-4 animate-spin" />
													加载项目中...
												</div>
											) : gitlabError ? (
												<div className="space-y-2">
													<div className="flex items-center gap-2 p-3 text-sm text-red-400 bg-red-900/20 rounded border border-red-800">
														<span className="codicon codicon-error"></span>
														<span className="flex-1 min-w-0 break-words">
															{gitlabError}
														</span>
													</div>
													<VSCodeButton
														appearance="secondary"
														onClick={() => {
															setGitlabError(null)
															setIsLoadingProjects(true)
															vscode.postMessage({
																type: "gitlabGetProjects",
															})
														}}
														className="w-full">
														重试
													</VSCodeButton>
												</div>
											) : (
												<SelectDropdown
													value={selectedProject?.id.toString() || ""}
													onChange={handleProjectSelect}
													placeholder="搜索并选择项目..."
													title="选择GitLab项目"
													options={gitlabProjectOptions}
												/>
											)}
										</div>

										{/* 显示选中的项目信息 */}
										{selectedProject && (
											<div className="p-2 border border-vscode-input-border rounded bg-vscode-input-background">
												<div className="text-xs">
													<div className="font-medium truncate">{selectedProject.name}</div>
													{selectedProject.description && (
														<div className="text-vscode-descriptionForeground truncate">
															{selectedProject.description}
														</div>
													)}
													<div className="text-vscode-descriptionForeground mt-1">
														默认分支: {selectedProject.default_branch}
													</div>
												</div>
											</div>
										)}

										{/* 分支选择 */}
										{selectedProject && (
											<div className="space-y-2">
												<label className="text-sm font-medium flex items-center gap-2">
													<GitBranch className="w-4 h-4" />
													选择分支
												</label>
												{isLoadingBranches ? (
													<div className="flex items-center gap-2 text-sm text-vscode-descriptionForeground">
														<Loader2 className="w-4 h-4 animate-spin" />
														加载分支中...
													</div>
												) : (
													<SelectDropdown
														value={selectedBranch || ""}
														onChange={handleBranchSelect}
														placeholder="搜索并选择分支..."
														title="选择GitLab分支"
														options={gitlabBranchOptions}
													/>
												)}
											</div>
										)}

										{/* 显示选中的分支 */}
										{selectedBranch && (
											<div className="text-xs text-vscode-descriptionForeground">
												已选择分支: <span className="font-medium">{selectedBranch}</span>
											</div>
										)}

										<VSCodeButton
											appearance="secondary"
											onClick={() => {
												vscode.postMessage({
													type: "deleteGitlabConfig",
												})
												setGitlabConfig(null)
												setSelectedProject(null)
												setSelectedBranch(null)
												setGitlabProjects([])
												setGitlabBranches([])
												setGitlabError(null)
												setGitlabVerified(false)
											}}
											className="w-full">
											{t("roomote:gitlab.disconnect")}
										</VSCodeButton>
									</div>
								)}
							</div>
						</div>
					</div>

					{/* 自定义指令 */}
					<div className="bg-vscode-sideBar-background rounded-lg border border-vscode-input-border">
						<div className="p-3 pb-2">
							<h3 className="text-sm font-medium text-vscode-foreground mb-2 flex items-center gap-2">
								<span className="codicon codicon-sparkle text-vscode-foreground"></span>
								<span className="flex-1">自定义指令</span>
								{!jiraConfig && <span className="text-vscode-errorForeground text-sm">*</span>}
								{jiraConfig && (
									<span className="text-xs text-vscode-descriptionForeground">(可选补充)</span>
								)}
							</h3>
							<VSCodeTextArea
								value={customInstructions}
								onInput={(e) => setCustomInstructions((e.target as HTMLTextAreaElement).value)}
								placeholder={
									jiraConfig
										? "补充额外的需求或约束条件（可选）..."
										: t("roomote:customInstructions.placeholder")
								}
								rows={4}
								resize="vertical"
								className="w-full"
							/>
						</div>

						{/* 控制栏 - 参考ChatTextArea的布局 */}
						<div className="flex justify-between items-center px-3 pb-3 pt-1">
							<div className="flex items-center gap-1 min-w-0">
								{/* 模式选择 */}
								<div className="shrink-0">
									<SelectDropdown
										value={mode}
										onChange={(value) => {
											setMode(value as Mode)
											// 同步到扩展状态
											vscode.postMessage({ type: "mode", text: value })
										}}
										placeholder="选择模式"
										title="选择AI模式"
										options={modeOptions}
										triggerClassName="w-full"
									/>
								</div>

								{/* Profile选择 */}
								<div className="flex-1 min-w-0 overflow-hidden">
									<SelectDropdown
										value={currentApiConfigName || ""}
										onChange={(profileName) => {
											setCurrentApiConfigName(profileName)
											// 切换全局 API 配置，保持与 ChatView 的一致性
											const configMeta = listApiConfigMeta?.find(
												(config) => config.name === profileName,
											)
											if (configMeta) {
												vscode.postMessage({
													type: "loadApiConfigurationById",
													text: configMeta.id,
												})
											}
										}}
										placeholder="选择 AI 配置"
										title="选择AI配置"
										options={profileOptions}
										triggerClassName="w-full text-ellipsis overflow-hidden"
									/>
								</div>
							</div>
						</div>
					</div>

					{/* 启动代理按钮 */}
					<div className="flex justify-center pt-1">
						<VSCodeButton
							appearance="primary"
							disabled={!canCreateTask || isCreatingTask}
							onClick={handleCreateTask}
							className="w-full sm:w-auto px-6 py-2">
							{isCreatingTask ? t("roomote:creating") : t("roomote:createTask")}
						</VSCodeButton>
					</div>
				</div>
			</TabContent>

			<div id="roo-portal" />
		</Tab>
	)
}
