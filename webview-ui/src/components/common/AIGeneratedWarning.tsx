import { memo } from "react"

interface AIGeneratedWarningProps {
	className?: string
}

const AIGeneratedWarning = ({ className = "" }: AIGeneratedWarningProps) => {
	return (
		<div className={`flex items-center gap-1.5 ${className}`} style={{ color: "#F5750C" }}>
			<span className="codicon codicon-info text-sm" style={{ color: "#F5750C" }}></span>
			<span className="text-xs">回答内容由AI生成，仅供参考，请仔细甄别。</span>
		</div>
	)
}

export default memo(AIGeneratedWarning)
