import { InternetSearchResult } from "@roo-code/types"
import { VSCodeLink } from "@vscode/webview-ui-toolkit/react"
import { useState } from "react"

interface InternetSearchResultsProps {
	internetSearchResults: InternetSearchResult[]
	internetSearchError: string | undefined
}

export const InternetSearchResults = ({ internetSearchResults, internetSearchError }: InternetSearchResultsProps) => {
	const [isExpanded, setIsExpanded] = useState(true)

	if (!isExpanded) {
		return (
			<div
				className="flex justify-between items-center h-8 px-1 cursor-pointer select-none hover:text-blue-500"
				onClick={() => setIsExpanded(true)}>
				<div className="flex items-center">
					<span className="codicon codicon-globe mr-1"></span>
					查看引用
				</div>

				<span className="codicon codicon-chevron-right"></span>
			</div>
		)
	}

	return (
		<>
			<div
				className="flex justify-between items-center cursor-pointer h-8 px-1 select-none hover:text-blue-500"
				onClick={() => setIsExpanded(false)}>
				<div className="flex items-center">
					<span className="codicon codicon-globe mr-1"></span>
					查看引用
				</div>
				<span className="codicon codicon-chevron-down"></span>
			</div>
			<div className="px-2">
				{internetSearchResults.map((result: InternetSearchResult) => (
					<VSCodeLink href={result.url} key={result.url} title={result.title} className="block h-6 truncate">
						{result.title}
					</VSCodeLink>
				))}
				{internetSearchError}
			</div>
		</>
	)
}
