{"extension.displayName": "Z<PERSON>lu Code (原名 Roo Cline)", "extension.description": "在你的編輯器中提供完整的 AI 代理開發團隊。", "command.newTask.title": "新建任務", "command.explainCode.title": "解釋程式碼", "command.fixCode.title": "修復程式碼", "command.improveCode.title": "改進程式碼", "command.addToContext.title": "新增到上下文", "command.openInNewTab.title": "在新分頁中開啟", "command.focusInput.title": "聚焦輸入框", "command.setCustomStoragePath.title": "設定自訂儲存路徑", "command.importSettings.title": "匯入設定", "command.terminal.addToContext.title": "將終端內容新增到上下文", "command.terminal.fixCommand.title": "修復此命令", "command.terminal.explainCommand.title": "解釋此命令", "command.acceptInput.title": "接受輸入/建議", "views.activitybar.title": "Zhanlu Code", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.mcpServers.title": "MCP 伺服器", "command.prompts.title": "模式", "command.history.title": "歷史記錄", "command.marketplace.title": "應用市場", "command.openInEditor.title": "在編輯器中開啟", "command.settings.title": "設定", "command.documentation.title": "文件", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "當啟用'始終批准執行操作'時可以自動執行的命令", "commands.deniedCommands.description": "將自動拒絕而無需請求批准的命令前綴。與允許命令衝突時，最長前綴匹配優先。新增 * 拒絕所有命令。", "commands.commandExecutionTimeout.description": "等待命令執行完成的最大時間（秒），逾時前（0 = 無逾時，1-600秒，預設：0秒）", "commands.commandTimeoutAllowlist.description": "從命令執行逾時中排除的命令前綴。符合這些前綴的命令將在沒有逾時限制的情況下執行。", "settings.vsCodeLmModelSelector.description": "VSCode 語言模型 API 的設定", "settings.vsCodeLmModelSelector.vendor.description": "語言模型供應商（例如：copilot）", "settings.vsCodeLmModelSelector.family.description": "語言模型系列（例如：gpt-4）", "settings.customStoragePath.description": "自訂儲存路徑。留空以使用預設位置。支援絕對路徑（例如：'D:\\ZhanluStorage'）", "settings.enableCodeActions.description": "啟用 Zhanlu Code 快速修復。", "settings.autoImportSettingsPath.description": "Zhanlu 設定檔案的路徑，用於在擴充功能啟動時自動匯入。支援絕對路徑和相對於主目錄的路徑（例如 '~/Documents/zhanlu-settings.json'）。留空以停用自動匯入。", "settings.useAgentRules.description": "為特定於代理的規則啟用 AGENTS.md 檔案的載入（請參閱 https://agent-rules.org/）"}