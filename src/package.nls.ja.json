{"extension.displayName": "Zhanlu Code (旧 Roo Cline)", "extension.description": "エディタ内のAIエージェントによる開発チーム。", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.activitybar.title": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.newTask.title": "新しいタスク", "command.mcpServers.title": "MCPサーバー", "command.prompts.title": "モード", "command.history.title": "履歴", "command.marketplace.title": "マーケットプレイス", "command.openInEditor.title": "エディタで開く", "command.settings.title": "設定", "command.documentation.title": "ドキュメント", "command.openInNewTab.title": "新しいタブで開く", "command.explainCode.title": "コードの説明", "command.fixCode.title": "コードの修正", "command.improveCode.title": "コードの改善", "command.addToContext.title": "コンテキストに追加", "command.focusInput.title": "入力フィールドにフォーカス", "command.setCustomStoragePath.title": "カスタムストレージパスの設定", "command.importSettings.title": "設定をインポート", "command.terminal.addToContext.title": "ターミナルの内容をコンテキストに追加", "command.terminal.fixCommand.title": "このコマンドを修正", "command.terminal.explainCommand.title": "このコマンドを説明", "command.acceptInput.title": "入力/提案を承認", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "'常に実行操作を承認する'が有効な場合に自動実行できるコマンド", "commands.deniedCommands.description": "承認を求めずに自動的に拒否されるコマンドプレフィックス。許可されたコマンドとの競合がある場合、最長プレフィックスマッチが優先されます。すべてのコマンドを拒否するには * を追加してください。", "commands.commandExecutionTimeout.description": "コマンド実行の完了を待つ最大時間（秒）、タイムアウトまで（0 = タイムアウトなし、1-600秒、デフォルト: 0秒）", "commands.commandTimeoutAllowlist.description": "コマンド実行タイムアウトから除外されるコマンドプレフィックス。これらのプレフィックスに一致するコマンドは、タイムアウト制限なしで実行されます。", "settings.vsCodeLmModelSelector.description": "VSCode 言語モデル API の設定", "settings.vsCodeLmModelSelector.vendor.description": "言語モデルのベンダー（例：copilot）", "settings.vsCodeLmModelSelector.family.description": "言語モデルのファミリー（例：gpt-4）", "settings.customStoragePath.description": "カスタムストレージパス。デフォルトの場所を使用する場合は空のままにします。絶対パスをサポートします（例：'D:\\ZhanluStorage'）", "settings.enableCodeActions.description": "Zhanlu Codeのクイック修正を有効にする。", "settings.autoImportSettingsPath.description": "拡張機能の起動時に自動的にインポートするZhanlu設定ファイルへのパス。絶対パスとホームディレクトリからの相対パスをサポートします（例：'~/Documents/zhanlu-settings.json'）。自動インポートを無効にするには、空のままにします。", "settings.useAgentRules.description": "エージェント固有のルールのためにAGENTS.mdファイルの読み込みを有効にします（参照：https://agent-rules.org/）"}