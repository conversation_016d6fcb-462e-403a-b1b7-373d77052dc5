import * as vscode from "vscode"

/**
 * 全局日志管理器 - 提供统一的日志输出接口
 * 可以直接使用，无需通过构造函数传递 outputChannel
 */
class GlobalLogger {
	private static instance: GlobalLogger
	private outputChannel?: vscode.OutputChannel

	private constructor() {}

	public static getInstance(): GlobalLogger {
		if (!GlobalLogger.instance) {
			GlobalLogger.instance = new GlobalLogger()
		}
		return GlobalLogger.instance
	}

	/**
	 * 初始化日志输出通道（通常在 extension.ts 中调用一次）
	 */
	public initialize(outputChannel: vscode.OutputChannel): void {
		this.outputChannel = outputChannel
	}

	/**
	 * 获取或创建 outputChannel
	 * 如果还未初始化，会自动创建一个新的
	 */
	private getOutputChannel(): vscode.OutputChannel {
		if (!this.outputChannel) {
			this.outputChannel = vscode.window.createOutputChannel("Zhanlu")
		}
		return this.outputChannel
	}

	/**
	 * 统一的日志输出方法
	 */
	public log(level: "info" | "error" | "warn", message: string, ...args: any[]): void {
		const timestamp = new Date().toISOString()
		const fullMessage = `[${timestamp}] [Zhanlu ${level.toUpperCase()}] ${message}`

		try {
			const channel = this.getOutputChannel()
			channel.appendLine(fullMessage)

			if (args.length > 0) {
				channel.appendLine(`Details: ${JSON.stringify(args, null, 2)}`)
			}
		} catch (error) {
			// Fallback to console if outputChannel fails
			switch (level) {
				case "error":
					console.error(fullMessage, ...args)
					break
				case "warn":
					console.warn(fullMessage, ...args)
					break
				default:
					console.log(fullMessage, ...args)
			}
		}
	}

	/**
	 * 便捷方法
	 */
	public info(message: string, ...args: any[]): void {
		this.log("info", message, ...args)
	}

	public error(message: string, ...args: any[]): void {
		this.log("error", message, ...args)
	}

	public warn(message: string, ...args: any[]): void {
		this.log("warn", message, ...args)
	}
}

// 导出单例实例和便捷函数
export const logger = GlobalLogger.getInstance()

// 便捷的顶级函数，可以直接导入使用
export function logInfo(message: string, ...args: any[]): void {
	logger.info(message, ...args)
}

export function logError(message: string, ...args: any[]): void {
	logger.error(message, ...args)
}

export function logWarn(message: string, ...args: any[]): void {
	logger.warn(message, ...args)
}
