import * as vscode from "vscode"

export class CopyPasteEditProvider implements vscode.DocumentPasteEditProvider {
	constructor(public context: vscode.ExtensionContext) {}

	/**
	 * Invoked on copy. This allows us to modify the `dataTransfer`
	 */
	prepareDocumentPaste(
		document: vscode.TextDocument,
		_ranges: readonly vscode.Range[],
		dataTransfer: vscode.DataTransfer,
		_token: vscode.CancellationToken,
	) {
		this.context.globalState.update("zhanlu.copyTextPlain", {
			copyTextPlain: dataTransfer.get("text/plain")?.value || "",
			fsPath: document.uri.fsPath,
		})
	}
}
