import { v4 as uuidv4 } from "uuid"
import { AESDecrypt, AESEncrypt, buildOpUrl, plugin_version } from "../../utils/eop"
import { ApiStream } from "../transform/stream"

// 多模态请求
export async function* createZhanluChatMessage(text: string, params: any): ApiStream {
	// console.log(11122, text, params)
	const { session_id, asl, baseUrl, maxTokens } = params
	const service_type = "chat"

	// const { max_tokens_picture_to_code } = props.msg.acepilotConfig
	// const { content, selectedOpenFiles } = props.msg
	const requestBody: any = {
		max_completion_tokens: maxTokens || 4096,
		messages: [
			{
				role: "user",
				content: text,
			},
		],
		...params.requestBody,
	}

	const headers: any = {
		plugin_version,
		plugin_type: "vscode",
		service_type,
		request: uuidv4(),
		session_id,
	}
	const urlPath = "/api/acepilot/zhanlu/chat"
	const requestUrl = buildOpUrl(urlPath, asl, baseUrl)
	// console.log(222555, headers, requestUrl, requestBody)
	const theBody = {
		data: AESEncrypt(JSON.stringify(requestBody), asl.token),
	}

	const response = await await fetch(requestUrl, {
		method: "post",
		headers: {
			"Content-Type": "application/json",
			...headers,
		},
		body: JSON.stringify(theBody),
	})

	if (!response.ok) {
		throw new Error(`HTTP error! status: ${response.status}`)
	}

	if (!response.body) {
		return
	}

	try {
		const reader = response.body.pipeThrough(new TextDecoderStream()).getReader()
		let encryptDataDebris = "" // 加密数据碎片处理
		const DATA_PRIFIX = "data:"
		let done: boolean = false

		while (!done) {
			if (!done && encryptDataDebris.indexOf(DATA_PRIFIX) === encryptDataDebris.lastIndexOf(DATA_PRIFIX)) {
				const resReadObj = await reader.read()
				done = resReadObj["done"]
				const theResReadObjValue = resReadObj["value"]
				if (theResReadObjValue) {
					// console.log(887744, theResReadObjValue)
					if (
						theResReadObjValue.includes("errorCode") &&
						theResReadObjValue.includes("errorMessage") &&
						theResReadObjValue.includes("state")
					) {
						const theErrorMsg = JSON.parse(theResReadObjValue)
						yield {
							type: "text",
							text: `后端异常：${theErrorMsg?.errorMessage}`,
						}
					} else {
						encryptDataDebris += theResReadObjValue.replaceAll("\n", "")
					}
				}
			}
			// console.log(1111, done)

			const theFirstIndex = encryptDataDebris.indexOf(DATA_PRIFIX)
			const theSecondIndex = encryptDataDebris.lastIndexOf(DATA_PRIFIX)
			let toDealData = ""
			if (theFirstIndex !== theSecondIndex && !done) {
				toDealData = encryptDataDebris.slice(theFirstIndex, theSecondIndex)
				// console.log(994400, toDealData.length)
				encryptDataDebris = encryptDataDebris.slice(theSecondIndex)
			} else if (done) {
				// console.log(9944, encryptDataDebris.length)
				toDealData = encryptDataDebris
				encryptDataDebris = ""
			} else {
				continue
			}

			let lines: any[] = []

			// console.log(1122, theFirstIndex, theSecondIndex)
			// console.log(11333, toDealData)
			// console.log(11444, encryptDataDebris)

			toDealData
				.split("data:")
				.filter((item) => item.trim() !== "")
				.forEach((item) => {
					const line = AESDecrypt(item, asl.token)
					if (line?.length) {
						lines.push(line)
					}
				})
			lines = lines.map((item) => JSON.parse(item)).filter((item) => item?.choices[0])
			//console.log(444333, JSON.parse(JSON.stringify(lines)))
			if (lines.some((item) => item.choices[0].finish_reason === "textScanNotPaased")) {
				yield {
					type: "text",
					text: "【湛卢无法回答您的问题，如果您有其他编程或技术相关的问题，湛卢很乐意为您提供帮助。】",
				}

				lines = []
			}

			lines = lines.filter((item) => item?.choices[0]?.delta?.content !== "")

			// console.log(444444, JSON.parse(JSON.stringify(lines)))
			// console.log(55555, lines.map(item => item.choices[0].delta.content).join(''))

			for (const jsonValue of lines) {
				yield {
					type: "text",
					text: jsonValue.choices[0].delta.content,
				}
				// props.msg.originalMarkdown += jsonValue.choices[0].delta.content;
			}

			if (done && !encryptDataDebris.length) {
				yield {
					type: "completion_result",
					text: "zhanlu_chat",
				}
				break
			}
		}
	} catch (error) {
		// console.log(999000, error)
		throw new Error(`报错：${error?.message}`)
	}
}
