import { v4 as uuidv4 } from "uuid"

import { AESDecrypt, AESEncrypt, buildOpUrl, plugin_version } from "../../utils/eop"
import { ApiStream } from "../transform/stream"

// 互联网搜索
export async function* createInternetSearchMessage(text: string, params: any): ApiStream {
	// console.log(11122, text, images, params)
	const { session_id, asl, baseUrl } = params
	const requestBody = {
		text,
	}
	const searchInternetsRequestId = uuidv4()

	const headers: any = {
		plugin_version,
		plugin_type: "vscode",
		session_id,
		request: searchInternetsRequestId,
	}
	const urlPath = "/api/acepilot/zhanlu/engines/offlineSearch"
	const requestUrl = buildOpUrl(urlPath, asl, baseUrl)
	// console.log(222555, headers, requestUrl, requestBody)
	const theBody = {
		data: AESEncrypt(JSON.stringify(requestBody), asl.token),
	}

	const response = await await fetch(requestUrl, {
		method: "post",
		headers: {
			"Content-Type": "application/json",
			...headers,
		},
		body: JSON.stringify(theBody),
	})

	if (!response.ok) {
		throw new Error(`HTTP error! status: ${response.status}`)
	}

	if (!response.body) {
		return
	}

	try {
		const reader = response.body.pipeThrough(new TextDecoderStream()).getReader()
		let encryptDataDebris = "" // 加密数据碎片处理
		const DATA_PRIFIX = "data:"
		let done: boolean = false

		while (!done) {
			if (!done && encryptDataDebris.indexOf(DATA_PRIFIX) === encryptDataDebris.lastIndexOf(DATA_PRIFIX)) {
				const resReadObj = await reader.read()
				done = resReadObj["done"]
				const theResReadObjValue = resReadObj["value"]
				if (theResReadObjValue) {
					// console.log(887744, theResReadObjValue)
					if (
						theResReadObjValue.includes("errorCode") &&
						theResReadObjValue.includes("errorMessage") &&
						theResReadObjValue.includes("state")
					) {
						const theErrorMsg = JSON.parse(theResReadObjValue)
						yield {
							type: "internet_search_error",
							text: theErrorMsg?.errorMessage || "后端异常：后端报错",
						}
					} else {
						encryptDataDebris += theResReadObjValue.replaceAll("\n", "")
					}
				}
			}
			// console.log(1111, done)

			const theFirstIndex = encryptDataDebris.indexOf(DATA_PRIFIX)
			const theSecondIndex = encryptDataDebris.lastIndexOf(DATA_PRIFIX)
			let toDealData = ""
			if (theFirstIndex !== theSecondIndex && !done) {
				toDealData = encryptDataDebris.slice(theFirstIndex, theSecondIndex)
				// console.log(994400, toDealData.length)
				encryptDataDebris = encryptDataDebris.slice(theSecondIndex)
			} else if (done) {
				// console.log(9944, encryptDataDebris.length)
				toDealData = encryptDataDebris
				encryptDataDebris = ""
			} else {
				continue
			}

			// console.log(1122, theFirstIndex, theSecondIndex)
			// console.log(11333, toDealData)
			// console.log(11444, encryptDataDebris)
			for (const item of toDealData.split("data:").filter((item) => item.trim() !== "")) {
				const line = AESDecrypt(item, asl.token)
				if (line?.length) {
					yield {
						type: "internet_search_result",
						text: JSON.parse(line),
					}
				}
			}

			if (done && !encryptDataDebris.length) {
				yield {
					type: "completion_result",
					text: searchInternetsRequestId,
				}
				break
			}
		}
	} catch (error) {
		// console.log(999000, error)
		throw new Error(`报错：${error?.message}`)
	}
}
