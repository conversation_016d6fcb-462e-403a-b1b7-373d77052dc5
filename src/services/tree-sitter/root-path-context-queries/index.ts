export { default as pythonClassDefinitionQuery } from "./python/class_definition"
export { default as pythonFunctionDefinitionQuery } from "./python/function_definition"

export { default as typescriptArrowFunctionQuery } from "./typescript/arrow_function"
export { default as typescriptClassDeclarationQuery } from "./typescript/class_declaration"
export { default as typescriptFunctionDeclarationQuery } from "./typescript/function_declaration"
export { default as typescriptGeneratorFunctionDeclarationQuery } from "./typescript/generator_function_declaration"
export { default as typescriptMethodDefinitionQuery } from "./typescript/method_definition"

export { default as phpClassDeclarationQuery } from "./php/class_declaration"
export { default as phpFunctionDefinitionQuery } from "./php/function_definition"
export { default as phpMethodDeclarationQuery } from "./php/method_declaration"

export { default as javaMethodDeclarationQuery } from "./java/method_declaration"

export { default as goFunctionDeclarationQuery } from "./go/function_declaration"

export { default as cppFunctionDefinitionQuery } from "./cpp/function_definition"
