export default `
(lexical_declaration
  (variable_declarator
    name: (identifier) @import
    value: (call_expression
      function: (identifier) @import_function
    )
    (#eq? @import_function "require")
  ))

(lexical_declaration 
  (variable_declarator
    name: (object_pattern ((shorthand_property_identifier_pattern) @import))
    value: (call_expression)))

(import_statement
  (import_clause
    (named_imports
      (import_specifier
        (identifier) @import))))

(import_statement
  (import_clause
    (identifier) @import ))
`
