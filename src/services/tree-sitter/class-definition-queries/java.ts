export default `
(class_declaration
  name: (identifier) @the-class-name
  body: (class_body
  (([(line_comment)(block_comment)])*
  (field_declaration
   (modifiers)* @the-field-modifiers
   type: [(type_identifier) (generic_type)] @the-field-type
   declarator: (variable_declarator) @the-field-declarator
  ))*
  
 (([(line_comment)(block_comment)])*
 (constructor_declaration
    (modifiers) @the-constructor-modifiers
    name: (identifier) @the-constructor-name
    parameters: (formal_parameters) @the-constructor-parameters
  ))*
 
  (([(line_comment)(block_comment)])*
  (method_declaration
    (modifiers) @the-method-modifiers
    type:[(type_identifier) (void_type) (generic_type)] @the-method-type
    name: (identifier) @the-method-name
    parameters: (formal_parameters) @the-method-parameters
))*)
)
`
