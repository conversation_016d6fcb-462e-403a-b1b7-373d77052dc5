export default `
(module
  ((comment)*
   (expression_statement 
    (assignment 
      left: ((identifier) @the-expression-name))) 
  )*
)

(function_definition  name: ((identifier)  @the-expression-name))

(class_definition  
  name: ((identifier) @the-class-name)
  superclasses: ((argument_list) @the-class-arguments)*
  body: (block
    ((comment)* (expression_statement (assignment left: ((identifier) @the-expression-name))))*
  )
)
 
 (decorated_definition 
  (decorator) 
  definition: 
    (class_definition  
      name: ((identifier) @the-class-name)
      superclasses: ((argument_list) @the-class-arguments)*
      body: (block
        ((comment)* (expression_statement (assignment left: ((identifier) @the-expression-name))))*
      )
 ))
`
