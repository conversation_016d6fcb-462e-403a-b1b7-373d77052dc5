export default `
(program
  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (object_pattern 
        (pair_pattern
          value: (identifier) @fun_name)))))*
  
  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (binary_expression)
  )))*

  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (function_expression)
  )))*

  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (object)
  )))*

  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (new_expression)
  )))*

   ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (array)
  )))*

  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (string)
  )))*

  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (member_expression)
  )))*
  
  ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
      value: (arrow_function))))*
      
   
    ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: [(object_pattern) ((identifier) @fun_name)]
      value:  (call_expression ) )))*
      
    ([(if_statement) (comment)]*
   (lexical_declaration 
    (variable_declarator
      name: (identifier) @fun_name
    )))*

  ((comment)*
   (function_declaration
    name: (identifier) @fun_name))*
   
  ((comment)*
  (expression_statement 
    (assignment_expression
      left: (member_expression 
        object: (identifier)
        property: (property_identifier) @fun_name
      )
      right: [(function_expression) (object ( method_definition)) (arrow_function)]
   )))*
)
`
