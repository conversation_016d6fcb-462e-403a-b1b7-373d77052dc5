import { Gitlab } from "@gitbeaker/rest"
import { GitLabConfig, GitLabProject, GitLabBranch } from "@roo-code/types"

export interface GitLabConnectionResult {
	success: boolean
	error?: string
	userInfo?: any
}

export interface GitLabProjectsResult {
	success: boolean
	error?: string
	projects?: GitLabProject[]
}

export interface GitLabBranchesResult {
	success: boolean
	error?: string
	branches?: GitLabBranch[]
}

export class GitLabService {
	private static instance: GitLabService
	private gitlab: InstanceType<typeof Gitlab> | null = null
	private config: GitLabConfig | null = null

	private constructor() {}

	static getInstance(): GitLabService {
		if (!GitLabService.instance) {
			GitLabService.instance = new GitLabService()
		}
		return GitLabService.instance
	}

	async connect(config: GitLabConfig): Promise<GitLabConnectionResult> {
		try {
			// Validate config
			if (!config.url.trim()) {
				return { success: false, error: "请输入 GitLab 服务器地址" }
			}
			if (!config.token.trim()) {
				return { success: false, error: "请输入 Personal Access Token" }
			}

			// Create GitLab client
			this.gitlab = new Gitlab({
				host: config.url,
				token: config.token,
			})

			// Test connection by getting current user info
			const userInfo = await this.gitlab.Users.showCurrentUser()

			// Store config for later use
			this.config = config

			return {
				success: true,
				userInfo,
			}
		} catch (error: any) {
			console.error("GitLab 连接失败:", error)
			this.gitlab = null
			this.config = null
			return {
				success: false,
				error: error.message || "连接失败，请检查配置信息",
			}
		}
	}

	async disconnect(): Promise<void> {
		this.gitlab = null
		this.config = null
	}

	async getProjects(): Promise<GitLabProjectsResult> {
		try {
			if (!this.gitlab) {
				return { success: false, error: "未连接到 GitLab" }
			}

			// Get user accessible projects
			const userProjects = await this.gitlab.Projects.all({
				membership: true,
				orderBy: "last_activity_at",
				sort: "desc",
				perPage: 50,
			})

			const projects: GitLabProject[] = userProjects.map((project: any) => ({
				id: project.id,
				name: project.name,
				path: project.path,
				path_with_namespace: project.path_with_namespace,
				description: project.description || "",
				default_branch: project.default_branch || "main",
				web_url: project.web_url,
				namespace: {
					id: project.namespace?.id || 0,
					name: project.namespace?.name || "",
					path: project.namespace?.path || "",
				},
			}))

			return {
				success: true,
				projects,
			}
		} catch (error: any) {
			console.error("获取项目失败:", error)
			return {
				success: false,
				error: error.message || "获取项目失败",
			}
		}
	}

	async getBranches(projectId: number | string): Promise<GitLabBranchesResult> {
		try {
			if (!this.gitlab) {
				return { success: false, error: "未连接到 GitLab" }
			}

			const projectBranches = await this.gitlab.Branches.all(projectId)

			const branches: GitLabBranch[] = projectBranches.map((branch: any) => ({
				name: branch.name,
				commit: {
					id: branch.commit?.id || "",
					short_id: branch.commit?.short_id || "",
					title: branch.commit?.title || "",
					message: branch.commit?.message || "",
					author_name: branch.commit?.author_name || "",
					author_email: branch.commit?.author_email || "",
					authored_date: branch.commit?.authored_date || "",
					committer_name: branch.commit?.committer_name || "",
					committer_email: branch.commit?.committer_email || "",
					committed_date: branch.commit?.committed_date || "",
				},
				merged: branch.merged || false,
				protected: branch.protected || false,
				developers_can_push: branch.developers_can_push || false,
				developers_can_merge: branch.developers_can_merge || false,
				can_push: branch.can_push || false,
				default: branch.default || false,
			}))

			return {
				success: true,
				branches,
			}
		} catch (error: any) {
			console.error("获取分支失败:", error)
			return {
				success: false,
				error: error.message || "获取分支失败",
			}
		}
	}

	isConnected(): boolean {
		return this.gitlab !== null && this.config !== null
	}

	getConfig(): GitLabConfig | null {
		return this.config
	}
}
