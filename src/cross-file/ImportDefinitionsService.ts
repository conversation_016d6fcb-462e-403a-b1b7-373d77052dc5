import * as vscode from "vscode"
import { QueryMatch, Point } from "web-tree-sitter"
import { LRUCache } from "lru-cache"

import { getFullLanguageName, getParserForFile, getQueryForFile, IGNORE_PATH_PATTERNS } from "./util/treeSitter"
import { CrossFileInfo, RangeInFile, RangeInFileWithContents } from "./symbol.model"
import { gotoDefinition, readFile, readRangeInFile } from "./util/ide"
import { execAsync } from "../utils/git"
import { getWorkspacePath } from "../utils/path"
import { createTimeoutPromise } from "../utils/promise.util"
import { queryJavaClassDefinition, queryJavascriptDependentFiles } from "./languages"

export class ImportDefinitionsService {
	crossFileCache: LRUCache<string, CrossFileInfo, unknown> = new LRUCache<string, CrossFileInfo>({
		max: 10,
	})
	private fileDiffCache = new LRUCache<string, string>({
		max: 10,
	})

	constructor() {
		vscode.window.onDidChangeActiveTextEditor((textEditor: vscode.TextEditor | undefined) => {
			if (textEditor && textEditor.document.uri.fsPath) {
				this.getCrossFileInfo(textEditor.document.uri).catch((e) =>
					console.warn(`Failed to initialize ImportDefinitionService: ${e.message}`),
				)

				this.gitDiffHeadFile(textEditor.document.uri.fsPath).catch((e) =>
					console.warn(`Failed to initialize gitDiffHeadFile: ${e.message}`),
				)
			}
		})
	}

	async get(uri: vscode.Uri): Promise<CrossFileInfo | undefined> {
		setTimeout(() => {
			this.getCrossFileInfo(uri)
		}, 0)
		return this.crossFileCache.get(uri.fsPath)
	}

	private async getCrossFileInfo(uri: vscode.Uri): Promise<any> {
		if (uri.fsPath.endsWith(".ipynb")) {
			// Commenting out this line was the solution to https://github.com/continuedev/continue/issues/1463
			return
		}
		const language = getFullLanguageName(uri.fsPath)
		if (language === "javascript") {
			return queryJavascriptDependentFiles(uri).then((res) => {
				if (res && Object.keys(res.imports).length) {
					this.crossFileCache.set(uri.fsPath, res)
				}
			})
		}

		const parser = await getParserForFile(uri.fsPath)
		if (!parser) {
			return
		}

		const fileContents = await readFile(uri)
		if (!fileContents.trim().length) {
			return
		}

		const ast = parser.parse(fileContents, undefined, {
			includedRanges: [
				{
					startIndex: 0,
					endIndex: 10_000,
					startPosition: { row: 0, column: 0 },
					endPosition: { row: 100, column: 0 },
				},
			],
		})

		const query = await getQueryForFile(uri.fsPath)

		if (!query) {
			return
		}

		if (!ast) {
			return
		}
		const matches: QueryMatch[] = query?.matches(ast.rootNode)
		const fileInfo: CrossFileInfo = {
			imports: {},
		}

		const workspaceFolder: vscode.WorkspaceFolder | undefined = vscode.workspace.getWorkspaceFolder(uri)
		const workspaceFolderName: string = workspaceFolder?.name || ""
		// console.log(1100, matches, workspaceFolder)

		for (const match of matches) {
			const startPosition: Point = match.captures[0].node.startPosition
			const defs: RangeInFile[] = await gotoDefinition(uri, {
				line: startPosition.row,
				character: startPosition.column,
			})
			// console.log(1111, defs)
			const pureDefs: RangeInFile[] = defs.filter((def) => {
				const isIgnoredPath =
					uri.fsPath === def.filepath ||
					IGNORE_PATH_PATTERNS[language]?.some((pattern) => pattern.test(def.filepath))
				return !isIgnoredPath && def.uri?.path.includes(workspaceFolderName)
			})
			// console.log(1122, startPosition, pureDefs)
			if (pureDefs.length) {
				if (language === "java") {
					const javaClassdefinitions: PromiseSettledResult<string>[] = await Promise.allSettled(
						pureDefs.map((def) => queryJavaClassDefinition(def.uri!)),
					)

					const rangeInFileWithContents: RangeInFileWithContents[] = []
					for (let i = 0; i < javaClassdefinitions.length; i++) {
						// console.log(112200, javaClassdefinitions[i])
						if (
							javaClassdefinitions[i].status === "fulfilled" &&
							(javaClassdefinitions[i] as PromiseFulfilledResult<string>).value?.length
						) {
							rangeInFileWithContents.push({
								...pureDefs[i],
								contents: (javaClassdefinitions[i] as PromiseFulfilledResult<string>).value,
							})
						}
					}
					if (rangeInFileWithContents.length) {
						fileInfo.imports[match.captures[0].node.text] = rangeInFileWithContents
					}
				} else {
					fileInfo.imports[match.captures[0].node.text] = await Promise.all(
						pureDefs.map(async (def) => ({
							...def,
							contents: await readRangeInFile(def.filepath, def.range),
						})),
					)
				}
			}
		}
		// console.log(1133, fileInfo)
		if (Object.keys(fileInfo.imports).length) {
			this.crossFileCache.set(uri.fsPath, fileInfo)
		}
	}

	private gitDiffHeadFile(fsPath: string): Promise<string> {
		// const theDiffStartTme = Date.now()
		return execAsync(`git diff HEAD -- ${fsPath}`, { cwd: getWorkspacePath() }).then(
			(res: { stdout: string; stderr: string }) => {
				// console.log(11122, res, )
				this.fileDiffCache.set(fsPath, res.stdout)
				return res.stdout
			},
		)
	}

	gitDiffHeadFileTimeout(fsPath: string, timeout = 0): Promise<any> {
		return Promise.race([
			this.gitDiffHeadFile(fsPath),
			createTimeoutPromise(timeout, this.fileDiffCache.get(fsPath) ?? ""),
		])
	}
}
