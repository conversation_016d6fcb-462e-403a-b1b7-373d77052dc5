import * as vscode from "vscode"
import { Parse<PERSON>, Tree, QueryCapture } from "web-tree-sitter"

import { getParserForFile, getQueryForFile, IGNORE_PATH_PATTERNS, LanguageName } from "../../cross-file/util/treeSitter"
import { gotoDefinition, readFile } from "../util/ide"
import { CrossFileInfo } from "../symbol.model"

// 获取文件第一层级函数、变量数据
export async function queryJavascriptDefinition(uri: vscode.Uri): Promise<string> {
	// const startTime = Date.now()
	const fileContents = await readFile(uri)
	const parser = await getParserForFile(uri.fsPath)
	if (!parser) {
		return ""
	}

	const ast: Tree | null = parser.parse(fileContents)
	if (!ast) {
		return ""
	}

	const query = await getQueryForFile(uri.fsPath, "class-definition-queries")
	if (!query) {
		return ""
	}
	const importQuery = await getQueryForFile(uri.fsPath)
	if (!importQuery) {
		return ""
	}
	const importQueryCaptures: QueryCapture[] = importQuery
		.captures(ast.rootNode)
		.filter((item) => item.name === "import")
	const queryCaptures: QueryCapture[] = query.captures(ast.rootNode).filter((item) => item.name === "fun_name")
	const pureCaptures: QueryCapture[] = []
	for (let i = 0; i < queryCaptures.length; i++) {
		// console.log(88442211, i, queryCaptures[i].node.startIndex !== queryCaptures[i - 1]?.node?.startIndex)
		if (
			queryCaptures[i].node.startIndex !== queryCaptures[i - 1]?.node?.startIndex &&
			!importQueryCaptures.some((item) => item.node.startIndex === queryCaptures[i].node.startIndex)
		) {
			pureCaptures.push(queryCaptures[i])
		}
	}

	// console.log(884422, queryCaptures)
	// console.log(88442200, pureCaptures)
	// console.log(88442211, importQueryCaptures)
	return Promise.allSettled(
		pureCaptures.map((match) =>
			vscode.commands.executeCommand(
				"_executeHoverProvider",
				uri,
				new vscode.Position(match.node.startPosition.row, match.node.startPosition.column),
			),
		),
	).then((res: PromiseSettledResult<unknown>[]): string => {
		// console.log(884433, res)
		let classDefinition = ""
		for (const resItem of res as PromiseSettledResult<vscode.Hover[]>[]) {
			if (resItem.status === "fulfilled" && (resItem.value[0]?.contents[0] as vscode.MarkdownString).value) {
				classDefinition += (resItem.value[0]?.contents[0] as vscode.MarkdownString).value
					.replace("```typescript\n", "")
					.replace("\n```\n", "\n")
			}
		}
		// console.log(884466, classDefinition, Date.now() - startTime)
		return classDefinition
	})
}

// 获取跨文件信息
export async function queryJavascriptDependentFiles(uri: vscode.Uri): Promise<any> {
	// const startTime = Date.now()
	const fileContents = await readFile(uri)
	// console.log(6600, uri)
	const parser = await getParserForFile(uri.fsPath)
	if (!parser) {
		return ""
	}

	const ast: Tree | null = parser.parse(fileContents)
	if (!ast) {
		return ""
	}

	const importQuery = await getQueryForFile(uri.fsPath)
	if (!importQuery) {
		return ""
	}
	const importQueryCaptures: QueryCapture[] = importQuery
		.captures(ast.rootNode)
		.filter((item) => item.name === "import")
	return Promise.allSettled(
		importQueryCaptures.map((match) =>
			gotoDefinition(uri, {
				line: match.node.startPosition.row,
				character: match.node.startPosition.column,
			}),
		),
	).then((defs: PromiseSettledResult<unknown>[]): any => {
		// console.log(6611, Date.now() - startTime, defs)
		const importQueryCapturesIndex: number[] = []
		const workspaceFolder: vscode.WorkspaceFolder | undefined = vscode.workspace.getWorkspaceFolder(uri)
		const workspaceFolderName: string = workspaceFolder?.name || ""
		for (let i = 0; i < defs.length; i++) {
			if (
				defs[i].status === "fulfilled" &&
				(defs[i] as any).value &&
				(defs[i] as any).value[0] &&
				(defs[i] as any).value[0].uri?.path !== uri.path &&
				!IGNORE_PATH_PATTERNS[LanguageName.JAVASCRIPT]?.some((pattern) =>
					pattern.test((defs[i] as any).value[0]?.filepath),
				) &&
				(defs[i] as any).value[0].uri?.path.includes(workspaceFolderName)
			) {
				importQueryCapturesIndex.push(i)
			}
		}
		// console.log(6622, Date.now() - startTime, importQueryCapturesIndex)
		if (!importQueryCapturesIndex.length) {
			return ""
		}

		return Promise.allSettled(
			importQueryCapturesIndex.map((theIndex) =>
				vscode.commands.executeCommand(
					"_executeHoverProvider",
					uri,
					new vscode.Position(
						importQueryCaptures[theIndex].node.startPosition.row,
						importQueryCaptures[theIndex].node.startPosition.column,
					),
				),
			),
		).then((res: PromiseSettledResult<unknown>[]): any => {
			// console.log(6633, Date.now() - startTime, res)
			const fileInfo: CrossFileInfo = {
				imports: {},
			}
			for (let i = 0; i < res.length; i++) {
				if (
					res[i].status === "fulfilled" &&
					((res[i] as any).value[0]?.contents[0] as vscode.MarkdownString).value
				) {
					fileInfo.imports[importQueryCaptures[importQueryCapturesIndex[i]].node.text] = [
						{
							...(defs[importQueryCapturesIndex[i]] as any).value[0],
							contents: ((res[i] as any).value[0]?.contents[0] as vscode.MarkdownString).value
								.replace("```typescript\n", "")
								.replace(
									`\nimport ${importQueryCaptures[importQueryCapturesIndex[i]].node.text}\n${"```"}`,
									"",
								)
								.replaceAll("(alias)", ""),
						},
					]
				}
			}
			// console.log(6644, Date.now() - startTime, fileInfo)
			return fileInfo
		})
	})
}
