import * as vscode from "vscode"
import { Parse<PERSON>, Tree, QueryCapture } from "web-tree-sitter"

import { getParserForFile, getQueryForFile } from "../../cross-file/util/treeSitter"
import { readFile } from "../util/ide"

// 获取跨文件class数据
export async function queryJavaClassDefinition(uri: vscode.Uri): Promise<string> {
	const fileContents = await readFile(uri)
	// console.log(7722, uri)
	const parser = await getParserForFile(uri.fsPath)
	if (!parser) {
		return ""
	}

	const ast: Tree | null = parser.parse(fileContents)
	if (!ast) {
		return ""
	}

	const query = await getQueryForFile(uri.fsPath, "class-definition-queries")
	if (!query) {
		return ""
	}

	const queryCaptures: QueryCapture[] = query.captures(ast.rootNode)
	let classDefinition = ""
	for (const queryCapture of queryCaptures) {
		// console.log(884422, queryCapture.name)
		// console.log(884433, queryCapture.node.text)

		switch (queryCapture.name) {
			case "the-class-name":
				classDefinition += classDefinition.length
					? `\n}\n\n class ${queryCapture.node.text} {\n`
					: `class ${queryCapture.node.text} {\n`
				break
			case "the-field-modifiers":
			case "the-field-type":
			case "the-constructor-modifiers":
			case "the-method-modifiers":
			case "the-method-type":
				classDefinition += `${queryCapture.node.text} `
				break
			case "the-field-declarator":
			case "the-constructor-parameters":
			case "the-method-parameters":
				classDefinition += `${queryCapture.node.text};\n`
				break
			case "the-constructor-name":
			case "the-method-name":
				classDefinition += `${queryCapture.node.text}`
				break
		}
	}
	if (classDefinition.length) {
		classDefinition += `}`
	}

	// console.log(8844, classDefinition)
	return classDefinition
}
