import * as vscode from "vscode"

const NO_FS_PROVIDER_ERROR = "ENOPRO"
const UNSUPPORTED_SCHEMES: Set<string> = new Set()

/**
 * Performs a file system operation on the given URI using the provided delegate function.
 *
 * @template T The type of the result returned by the delegate function.
 * @param uri The URI on which the file system operation is to be performed.
 * @param delegate A function that performs the desired operation on the given URI.
 * @param ignoreMissingProviders Whether to ignore errors caused by missing file system providers. Defaults to `true`.
 * @returns A promise that resolves to the result of the delegate function, or `null` if the operation is skipped due to unsupported schemes or missing providers.
 * @throws Re-throws any error encountered during the operation, except for missing provider errors when `ignoreMissingProviders` is `true`.
 */
async function fsOperation<T>(
	uri: vscode.Uri,
	delegate: (uri: vscode.Uri) => T,
	ignoreMissingProviders: boolean = true,
): Promise<T | null> {
	const scheme = uri.scheme
	if (ignoreMissingProviders && UNSUPPORTED_SCHEMES.has(scheme)) {
		return null
	}
	try {
		return await delegate(uri)
	} catch (err: any) {
		if (
			ignoreMissingProviders &&
			//see https://github.com/microsoft/vscode/blob/c9c54f9e775e5f57d97bef796797b5bc670c8150/src/vs/workbench/api/common/extHostFileSystemConsumer.ts#L230
			(err.name === NO_FS_PROVIDER_ERROR || err.message?.includes(NO_FS_PROVIDER_ERROR))
		) {
			UNSUPPORTED_SCHEMES.add(scheme)
			console.log(`Ignoring missing provider error:`, err.message)
			return null
		}
		throw err
	}
}

/**
 * Read the entire contents of a file from the given URI.
 *
 * @param uri - The URI of the file to read.
 * @param ignoreMissingProviders - Optional flag to ignore missing file system providers for unsupported schemes.
 *                                 Defaults to `true`.
 * @returns A promise that resolves to the file content as a `Uint8Array`, or `null` if the scheme is unsupported
 *          or the provider is missing and `ignoreMissingProviders` is `true`.
 *          If `ignoreMissingProviders` is `false`, it will throw an error for unsupported schemes or missing providers.
 * @throws Will rethrow any error that is not related to missing providers or unsupported schemes.
 */
export async function readFileUtil(
	uri: vscode.Uri,
	ignoreMissingProviders: boolean = true,
): Promise<Uint8Array | null> {
	return await fsOperation(
		uri,
		async (u) => {
			return await vscode.workspace.fs.readFile(u)
		},
		ignoreMissingProviders,
	)
}

export async function readRangeInFileUtil(uri: vscode.Uri, range: vscode.Range): Promise<string> {
	const buffer = await readFileUtil(uri)
	if (buffer === null) {
		return ""
	}
	const contents = new TextDecoder().decode(buffer)
	const lines = contents.split("\n")
	return `${lines.slice(range.start.line, range.end.line).join("\n")}\n${lines[
		range.end.line < lines.length - 1 ? range.end.line : lines.length - 1
	].slice(0, range.end.character)}`
}
