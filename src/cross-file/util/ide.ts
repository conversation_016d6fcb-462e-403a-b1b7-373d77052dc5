import * as vscode from "vscode"

import { readRangeInFileUtil } from "./ideUtils"
import { RangeInFile, Position, Range } from "../symbol.model"
import { executeGotoProvider } from "../lsp"

// 读取文件内容
export async function readFile(uri: vscode.Uri): Promise<string> {
	try {
		// const uri = vscode.Uri.file(fileUri)
		const textDocument = await vscode.workspace.openTextDocument(uri)
		const contents = textDocument.getText()
		return contents
	} catch (e) {
		return ""
	}
}

export async function gotoDefinition(uri: vscode.Uri, position: Position): Promise<RangeInFile[]> {
	const result = await executeGotoProvider({
		uri,
		...position,
		name: "vscode.executeDefinitionProvider",
	})

	return result
}

export function readRangeInFile(fileUri: string, range: Range): Promise<string> {
	// 修复 Windows 路径处理问题
	// 如果 fileUri 是绝对路径而不是 URI，使用 vscode.Uri.file() 而不是 vscode.Uri.parse()
	let uri: vscode.Uri
	if (fileUri.startsWith("file://")) {
		// 已经是 URI 格式
		uri = vscode.Uri.parse(fileUri)
	} else {
		// 是文件路径，使用 vscode.Uri.file() 来正确处理 Windows 路径
		uri = vscode.Uri.file(fileUri)
	}

	return readRangeInFileUtil(
		uri,
		new vscode.Range(
			new vscode.Position(range.start.line, range.start.character),
			new vscode.Position(range.end.line, range.end.character),
		),
	)
}

// 处理跨文件路径基于workspace
export function changeToWorkspacePath(path: string) {
	if (vscode.workspace.workspaceFolders && vscode.workspace.workspaceFolders[0]) {
		return path.slice(
			vscode.workspace.workspaceFolders[0].uri.path.length - vscode.workspace.workspaceFolders[0].name.length,
		)
	}
	return path
}
