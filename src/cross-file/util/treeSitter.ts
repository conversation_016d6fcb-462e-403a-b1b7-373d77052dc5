import * as vscode from "vscode"
import { Language, Query, Tree, Node } from "web-tree-sitter"

import { getUriFileExtension } from "./uri"
import { FileSymbolMap, SymbolWithRange } from "../symbol.model"
import { readFile } from "./ide"
import {
	cpptImportQuery,
	goImportQuery,
	javaImportQuery,
	pythonImportQuery,
	typescriptImportQuery,
	javascriptImportQuery,
} from "../../services/tree-sitter/import-queries"
import { loadLanguage, loadRequiredLanguageParsers } from "../../services/tree-sitter/languageParser"
import {
	cppFunctionDefinitionQuery,
	goFunctionDeclarationQuery,
	javaMethodDeclarationQuery,
	phpClassDeclarationQuery,
	phpFunctionDefinitionQuery,
	phpMethodDeclarationQuery,
	pythonClassDefinitionQuery,
	pythonFunctionDefinitionQuery,
	typescriptArrowFunctionQuery,
	typescriptClassDeclarationQuery,
	typescriptFunctionDeclarationQuery,
	typescriptGeneratorFunctionDeclarationQuery,
	typescriptMethodDefinitionQuery,
} from "../../services/tree-sitter/root-path-context-queries"
import {
	javaClassDefinitionQuery,
	javascriptClassDefinitionQuery,
} from "../../services/tree-sitter/class-definition-queries"

export enum LanguageName {
	CPP = "cpp",
	C_SHARP = "c_sharp",
	C = "c",
	CSS = "css",
	PHP = "php",
	BASH = "bash",
	JSON = "json",
	TYPESCRIPT = "typescript",
	TYPESCRIPTREACT = "typescriptreact",
	TYPESCRIPTREACTNATIVE = "typescriptreactnative",
	TSX = "tsx",
	ELM = "elm",
	JAVASCRIPT = "javascript",
	PYTHON = "python",
	ELISP = "elisp",
	ELIXIR = "elixir",
	GO = "go",
	EMBEDDED_TEMPLATE = "embedded_template",
	HTML = "html",
	JAVA = "java",
	LUA = "lua",
	OCAML = "ocaml",
	QL = "ql",
	RESCRIPT = "rescript",
	RUBY = "ruby",
	RUST = "rust",
	SYSTEMRDL = "systemrdl",
	TOML = "toml",
	SOLIDITY = "solidity",
}

export const supportedLanguages: { [key: string]: LanguageName } = {
	cpp: LanguageName.CPP,
	hpp: LanguageName.CPP,
	cc: LanguageName.CPP,
	cxx: LanguageName.CPP,
	hxx: LanguageName.CPP,
	cp: LanguageName.CPP,
	hh: LanguageName.CPP,
	inc: LanguageName.CPP,
	// Depended on this PR: https://github.com/tree-sitter/tree-sitter-cpp/pull/173
	// ccm: LanguageName.CPP,
	// c++m: LanguageName.CPP,
	// cppm: LanguageName.CPP,
	// cxxm: LanguageName.CPP,
	cs: LanguageName.C_SHARP,
	c: LanguageName.C,
	h: LanguageName.C,
	css: LanguageName.CSS,
	php: LanguageName.PHP,
	phtml: LanguageName.PHP,
	php3: LanguageName.PHP,
	php4: LanguageName.PHP,
	php5: LanguageName.PHP,
	php7: LanguageName.PHP,
	phps: LanguageName.PHP,
	"php-s": LanguageName.PHP,
	bash: LanguageName.BASH,
	sh: LanguageName.BASH,
	json: LanguageName.JSON,
	ts: LanguageName.TYPESCRIPT,
	mts: LanguageName.TYPESCRIPT,
	cts: LanguageName.TYPESCRIPT,
	tsx: LanguageName.TSX,
	// vue: LanguageName.VUE,  // tree-sitter-vue parser is broken
	// The .wasm file being used is faulty, and yaml is split line-by-line anyway for the most part
	// yaml: LanguageName.YAML,
	// yml: LanguageName.YAML,
	elm: LanguageName.ELM,
	js: LanguageName.JAVASCRIPT,
	jsx: LanguageName.JAVASCRIPT,
	mjs: LanguageName.JAVASCRIPT,
	cjs: LanguageName.JAVASCRIPT,
	py: LanguageName.PYTHON,
	// ipynb: LanguageName.PYTHON, // It contains Python, but the file format is a ton of JSON.
	pyw: LanguageName.PYTHON,
	pyi: LanguageName.PYTHON,
	el: LanguageName.ELISP,
	emacs: LanguageName.ELISP,
	ex: LanguageName.ELIXIR,
	exs: LanguageName.ELIXIR,
	go: LanguageName.GO,
	eex: LanguageName.EMBEDDED_TEMPLATE,
	heex: LanguageName.EMBEDDED_TEMPLATE,
	leex: LanguageName.EMBEDDED_TEMPLATE,
	html: LanguageName.HTML,
	htm: LanguageName.HTML,
	java: LanguageName.JAVA,
	lua: LanguageName.LUA,
	luau: LanguageName.LUA,
	ocaml: LanguageName.OCAML,
	ml: LanguageName.OCAML,
	mli: LanguageName.OCAML,
	ql: LanguageName.QL,
	res: LanguageName.RESCRIPT,
	resi: LanguageName.RESCRIPT,
	rb: LanguageName.RUBY,
	erb: LanguageName.RUBY,
	rs: LanguageName.RUST,
	rdl: LanguageName.SYSTEMRDL,
	toml: LanguageName.TOML,
	sol: LanguageName.SOLIDITY,

	// jl: LanguageName.JULIA,
	// swift: LanguageName.SWIFT,
	// kt: LanguageName.KOTLIN,
	// scala: LanguageName.SCALA,
}

export const IGNORE_PATH_PATTERNS: Partial<Record<LanguageName, RegExp[]>> = {
	[LanguageName.TYPESCRIPT]: [/.*node_modules/],
	[LanguageName.JAVASCRIPT]: [/.*node_modules/],
	[LanguageName.TYPESCRIPTREACT]: [/.*node_modules/],
	[LanguageName.TYPESCRIPTREACTNATIVE]: [/.*node_modules/],
	[LanguageName.JAVA]: [
		/.*\/target\/classes\/.*/, // Maven编译输出
		/.*\/build\/classes\/.*/, // Gradle编译输出
		/.*\.jar!\/.*/, // JAR包内部文件
		/.*\/\.m2\/repository\/.*/, // Maven本地仓库
		/.*\/\.gradle\/caches\/.*/, // Gradle缓存
		/.*\/src\/test\/.*/, // 测试文件
		/java\/lang\/.*/, // Java标准库
		/java\/util\/.*/, // Java工具类
		/java\/io\/.*/, // Java IO类
		/javax\/.*/, // Java扩展库
		/org\/springframework\/.*/, // Spring框架
		/org\/apache\/.*/, // Apache库
		/com\/fasterxml\/jackson\/.*/, // Jackson库
		/lombok\/.*/, // Lombok库
	],
}

// Use the same initialization approach as languageParser.ts to avoid conflicts
async function ensureTreeSitterInitialized() {
	// Use the same initialization logic as in languageParser.ts
	// This ensures we don't have conflicting initialization states
	await loadRequiredLanguageParsers([])
}
export async function getParserForFile(filepath: string) {
	try {
		await ensureTreeSitterInitialized()
		const { Parser } = require("web-tree-sitter")
		const parser = new Parser()

		const language = await getLanguageForFile(filepath)
		if (!language) {
			return undefined
		}

		parser.setLanguage(language)

		return parser
	} catch (e) {
		console.debug("Unable to load language for file", filepath, e)
		return undefined
	}
}

// Loading the wasm files to create a Language object is an expensive operation and with
// sufficient number of files can result in errors, instead keep a map of language name
// to Language object
const nameToLanguage = new Map<string, Language>()

export async function getLanguageForFile(filepath: string): Promise<Language | undefined> {
	try {
		await ensureTreeSitterInitialized()
		const extension = getUriFileExtension(filepath)

		const languageName = supportedLanguages[extension]

		if (!languageName) {
			return undefined
		}
		let language = nameToLanguage.get(languageName)

		if (!language) {
			// Pass the correct sourceDirectory parameter like in languageParser.ts
			language = await loadLanguage(languageName, typeof __dirname !== "undefined" ? __dirname : undefined)
			if (language) {
				nameToLanguage.set(languageName, language)
			}
		}
		return language
	} catch (e) {
		console.debug("Unable to load language for file", filepath, e)
		return undefined
	}
}

export const getFullLanguageName = (filepath: string) => {
	const extension = getUriFileExtension(filepath)
	return supportedLanguages[extension]
}

export async function getQueryForFile(filepath: string, type = "import-query"): Promise<Query | undefined> {
	const languageParser = await getLanguageForFile(filepath)
	if (!languageParser) {
		return undefined
	}

	// Ensure we use the same Query constructor as languageParser.ts
	await ensureTreeSitterInitialized()
	const { Query } = require("web-tree-sitter")

	const language = getFullLanguageName(filepath)
	// console.log(1133, language)
	switch (language) {
		case "python":
			if (type === "import-query") {
				return new Query(languageParser, pythonImportQuery)
			} else if (type === "class_definition") {
				return new Query(languageParser, pythonClassDefinitionQuery)
			} else if (type === "function_definition") {
				return new Query(languageParser, pythonFunctionDefinitionQuery)
			}
			return undefined
		case "typescript":
			if (type === "import-query") {
				return new Query(languageParser, typescriptImportQuery)
			} else if (type === "arrow_function") {
				return new Query(languageParser, typescriptArrowFunctionQuery)
			} else if (type === "class_declaration") {
				return new Query(languageParser, typescriptClassDeclarationQuery)
			} else if (type === "function_declaration") {
				return new Query(languageParser, typescriptFunctionDeclarationQuery)
			} else if (type === "generator_function_declaration") {
				return new Query(languageParser, typescriptGeneratorFunctionDeclarationQuery)
			} else if (type === "method_definition") {
				return new Query(languageParser, typescriptMethodDefinitionQuery)
			}
			return undefined
		case "javascript":
			if (type === "import-query") {
				return new Query(languageParser, javascriptImportQuery)
			} else if (type === "class-definition-queries") {
				return new Query(languageParser, javascriptClassDefinitionQuery)
			}
			return undefined
		case "cpp":
			if (type === "import-query") {
				return new Query(languageParser, cpptImportQuery)
			} else if (type === "function_definition") {
				return new Query(languageParser, cppFunctionDefinitionQuery)
			}
			return undefined
		case "java":
			if (type === "import-query") {
				return new Query(languageParser, javaImportQuery)
			} else if (type === "method_declaration") {
				return new Query(languageParser, javaMethodDeclarationQuery)
			} else if (type === "class-definition-queries") {
				return new Query(languageParser, javaClassDefinitionQuery)
			}
			return undefined
		case "go":
			if (type === "import-query") {
				return new Query(languageParser, goImportQuery)
			} else if (type === "function_declaration") {
				return new Query(languageParser, goFunctionDeclarationQuery)
			}
			return undefined
		case "php":
			if (type === "class_declaration") {
				return new Query(languageParser, phpClassDeclarationQuery)
			} else if (type === "function_definition") {
				return new Query(languageParser, phpFunctionDefinitionQuery)
			} else if (type === "method_declaration") {
				return new Query(languageParser, phpMethodDeclarationQuery)
			}
			return undefined
		default:
			return undefined
	}
}

// See https://tree-sitter.github.io/tree-sitter/using-parsers
const GET_SYMBOLS_FOR_NODE_TYPES: Node["type"][] = [
	"class_declaration",
	"class_definition",
	"function_item", // function name = first "identifier" child
	"function_definition",
	"method_declaration", // method name = first "identifier" child
	"method_definition",
	"generator_function_declaration",
	// property_identifier
	// field_declaration
	// "arrow_function",
]

export async function getSymbolsForFile(uri: vscode.Uri, contents: string): Promise<SymbolWithRange[] | undefined> {
	const parser = await getParserForFile(uri.fsPath)
	if (!parser) {
		return
	}

	let tree: Tree | null
	try {
		tree = parser.parse(contents)
	} catch (e) {
		console.log(`Error parsing file: ${uri.fsPath}`)
		return
	}

	if (!tree) {
		console.log(`Failed to parse file: ${uri.fsPath}`)
		return
	}
	// console.log(`file: ${filepath}`);

	// Function to recursively find all named nodes (classes and functions)
	const symbols: SymbolWithRange[] = []
	function findNamedNodesRecursive(node: Node) {
		// console.log(`node: ${node.type}, ${node.text}`);
		if (GET_SYMBOLS_FOR_NODE_TYPES.includes(node.type)) {
			// console.log(`parent: ${node.type}, ${node.text.substring(0, 200)}`);
			// node.children.forEach((child) => {
			//   console.log(`child: ${child.type}, ${child.text}`);
			// });

			// Empirically, the actual name is the last identifier in the node
			// Especially with languages where return type is declared before the name
			// TODO use findLast in newer version of node target
			let identifier: Node | undefined = undefined
			for (let i = node.children.length - 1; i >= 0; i--) {
				const child = node.children[i]
				if (child && (child.type === "identifier" || child.type === "property_identifier")) {
					identifier = child
					break
				}
			}

			if (identifier?.text) {
				symbols.push({
					filepath: uri.fsPath,
					type: node.type,
					name: identifier.text,
					range: {
						start: {
							character: node.startPosition.column,
							line: node.startPosition.row,
						},
						end: {
							character: node.endPosition.column + 1,
							line: node.endPosition.row + 1,
						},
					},
					content: node.text,
				})
			}
		}
		node.children.forEach((child) => {
			if (child) {
				findNamedNodesRecursive(child)
			}
		})
	}
	findNamedNodesRecursive(tree.rootNode)
	return symbols
}

export async function getSymbolsForManyFiles(uris: vscode.Uri[]): Promise<FileSymbolMap> {
	const filesAndSymbols = await Promise.all(
		uris.map(async (uri): Promise<[string, SymbolWithRange[]]> => {
			const contents = await readFile(uri)
			let symbols = undefined
			try {
				symbols = await getSymbolsForFile(uri, contents)
			} catch (e) {
				console.error(`Failed to get symbols for ${uri}:`, e)
			}
			return [uri.fsPath, symbols ?? []]
		}),
	)
	return Object.fromEntries(filesAndSymbols)
}
