import * as vscode from "vscode"

import { debounce } from "../decorators"

export class PrecalculatedLruCache<V> {
	private items: [string, V][] = []
	constructor(
		private readonly calculateValue: (uri: vscode.Uri) => Promise<V | null>,
		private readonly N: number,
	) {}

	async initKey(uri: vscode.Uri) {
		// Maintain LRU
		const index = this.items.findIndex((item) => item[0] === uri.fsPath)

		if (index < 0) {
			// Calculate info for new file
			const value: V | null = await this.calculateValue(uri)
			// console.log(8851, uri, value, this.items)
			if (value === null) {
				return
			}

			this.items.push([uri.fsPath, value])
			if (this.items.length > this.N) {
				this.items.shift()
			}
		} else {
			// Move to end of array, since it was recently used
			const [item] = this.items.splice(index, 1)
			this.items.push(item)
		}
		// console.log(8852, uri, this.items)
	}

	@debounce(500)
	async updateKey(uri: vscode.Uri) {
		const value: V | null = await this.calculateValue(uri)
		if (value) {
			const index = this.items.findIndex((item) => item[0] === uri.fsPath)
			this.items[index][1] = value
		}
	}

	get(uri: vscode.Uri): V | undefined {
		const result = this.items.find((item) => item[0] === uri.fsPath)?.[1]
		if (!result) {
			this.initKey(uri)
		} else {
			// 更新引入跨文件
			this.updateKey(uri)
		}
		return result
	}
}
