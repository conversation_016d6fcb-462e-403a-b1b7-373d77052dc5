import * as vscode from "vscode"
import { createHash } from "crypto"
import { LRUCache } from "lru-cache"
import { Node, Query, QueryMatch, Point, Tree } from "web-tree-sitter"

import { getFullLanguageName, getQueryForFile, IGNORE_PATH_PATTERNS, LanguageName } from "./util/treeSitter"
import { AutocompleteSnippetDeprecated, CrossFile } from "./symbol.model"
import { ImportDefinitionsService } from "./ImportDefinitionsService"
import { changeToWorkspacePath, gotoDefinition, readRangeInFile } from "./util/ide"
import { AstPath, getAst, getTreePathAtCursor } from "./util/ast"
import { createTimeoutPromise } from "../utils/promise.util"
import { queryJavaClassDefinition, queryJavascriptDefinition } from "./languages"

// function getSyntaxTreeString(
//   node: Parser.SyntaxNode,
//   indent: string = "",
// ): string {
//   let result = "";
//   const nodeInfo = `${node.type} [${node.startPosition.row}:${node.startPosition.column} - ${node.endPosition.row}:${node.endPosition.column}]`;
//   result += `${indent}${nodeInfo}\n`;

//   for (const child of node.children) {
//     result += getSyntaxTreeString(child, indent + "  ");
//   }

//   return result;
// }

export class RootPathContextService {
	private cache = new LRUCache<string, AutocompleteSnippetDeprecated[]>({
		max: 100,
	})
	private fileRootPathContextCache = new LRUCache<string, CrossFile[]>({
		max: 20,
	}) // 文件级

	constructor(private readonly importDefinitionsService: ImportDefinitionsService) {
		vscode.window.onDidChangeActiveTextEditor((textEditor: vscode.TextEditor | undefined) => {
			if (textEditor) {
				this.initRootPathContext(textEditor.document)
			}
		})

		if (vscode.window.activeTextEditor && vscode.window.activeTextEditor.document.languageId === "java") {
			this.initRootPathContext(vscode.window.activeTextEditor.document)
		}
	}

	private static getNodeId(node: Node): string {
		return `${node.startIndex}`
	}

	async getRootPathContextFromCache(fsPath: string): Promise<CrossFile[]> {
		return this.fileRootPathContextCache.get(fsPath) || []
	}

	private static TYPES_TO_USE = new Set([
		"arrow_function",
		"generator_function_declaration",
		"program",
		"function_declaration",
		"function_definition",
		"method_definition",
		"method_declaration",
		"class_declaration",
		"class_definition",
	])

	/**
	 * Key comes from hash of parent key and node type and node id.
	 */
	private static keyFromNode(parentKey: string, astNode: Node): string {
		return createHash("sha256")
			.update(parentKey)
			.update(astNode.type)
			.update(RootPathContextService.getNodeId(astNode))
			.digest("hex")
	}

	private async getSnippetsForNode(uri: vscode.Uri, node: Node): Promise<AutocompleteSnippetDeprecated[]> {
		const snippets: AutocompleteSnippetDeprecated[] = []
		const language = getFullLanguageName(uri.fsPath)

		let query: Query | undefined
		switch (node.type) {
			case "program":
				this.importDefinitionsService.get(uri)
				break
			default:
				// const type = node.type;
				// console.log(getSyntaxTreeString(node));

				query = await getQueryForFile(uri.fsPath, node.type)
				break
		}

		if (!query) {
			return snippets
		}

		const queries = query.matches(node).map(async (match: QueryMatch) => {
			for (const item of match.captures) {
				const endPosition = item.node.endPosition
				const newSnippets = await this.getSnippets(uri, endPosition, language)
				snippets.push(...newSnippets)
			}
		})

		await Promise.all(queries)

		return snippets
	}

	private async getSnippets(
		uri: vscode.Uri,
		endPosition: Point,
		language: LanguageName,
	): Promise<AutocompleteSnippetDeprecated[]> {
		const definitions = await gotoDefinition(uri, {
			line: endPosition.row,
			character: endPosition.column,
		})
		const newSnippets = await Promise.all(
			definitions
				.filter((definition) => {
					const isIgnoredPath = IGNORE_PATH_PATTERNS[language]?.some((pattern) =>
						pattern.test(definition.filepath),
					)

					return !isIgnoredPath
				})
				.map(async (def) => ({
					...def,
					contents: await readRangeInFile(def.filepath, def.range),
				})),
		)

		return newSnippets
	}

	async getContextForPath(
		uri: vscode.Uri,
		astPath: AstPath,
		// cursorIndex: number,
	): Promise<CrossFile[]> {
		const snippets: CrossFile[] = []

		let parentKey = uri.fsPath
		for (const astNode of astPath.filter((node) => RootPathContextService.TYPES_TO_USE.has(node.type))) {
			const key = RootPathContextService.keyFromNode(parentKey, astNode)
			// const type = astNode.type;

			const foundInCache = this.cache.get(key)
			let newSnippets

			// 确保对数据进行更新
			if (foundInCache) {
				newSnippets = foundInCache
				this.getSnippetsForNode(uri, astNode).then((res) => this.cache.set(key, res))
			} else {
				newSnippets = await this.getSnippetsForNode(uri, astNode).then((res) => {
					this.cache.set(key, res)
					return res
				})
			}

			const formattedSnippets: CrossFile[] = newSnippets.map((item) => ({
				kind: "RootPathSnippets",
				path: item.filepath,
				content: item.contents,
				depth: null,
			}))

			snippets.push(...formattedSnippets)

			parentKey = key
		}

		return snippets
	}

	initRootPathContext(document: vscode.TextDocument, position?: vscode.Position): Promise<any> {
		switch (document.languageId) {
			case "java":
				return queryJavaClassDefinition(document.uri).then((content) => {
					const theCross: CrossFile[] = []
					if (content.length) {
						theCross.push({
							kind: "RootPathSnippets",
							path: changeToWorkspacePath(document.uri.path),
							content,
							depth: null,
						})

						this.fileRootPathContextCache.set(document.uri.fsPath, theCross)
					}

					return theCross
				})
			case "javascript":
				return queryJavascriptDefinition(document.uri).then((content) => {
					const theCross: CrossFile[] = []
					if (content.length) {
						theCross.push({
							kind: "RootPathSnippets",
							path: changeToWorkspacePath(document.uri.path),
							content,
							depth: null,
						})

						this.fileRootPathContextCache.set(document.uri.fsPath, theCross)
					}

					return theCross
				})
		}

		return getAst(document.uri.fsPath, document.getText())
			.then((ast: Tree | undefined) => {
				if (ast) {
					return getTreePathAtCursor(
						ast,
						position
							? document.getText(new vscode.Range(new vscode.Position(0, 0), position)).length
							: document.getText().trim().length,
					)
				}
				return undefined
			})
			.then((treePath: AstPath | undefined) => {
				if (treePath) {
					return this.getContextForPath(document.uri, treePath)
				}
				return undefined
			})
			.then((rootPathSnippets: CrossFile[] | undefined) => {
				if (!position && rootPathSnippets?.length) {
					this.fileRootPathContextCache.set(document.uri.fsPath, rootPathSnippets)
				}
				return rootPathSnippets || ""
			})
	}

	// 根节点-光标所在的子节点的语法树路径中检索到的片段
	getRootPathSnippets(document: vscode.TextDocument, position: vscode.Position, timeout = 0): Promise<any> {
		if (timeout === 0) {
			setTimeout(() => {
				this.initRootPathContext(document)
			}, 0)
			return this.getRootPathContextFromCache(document.uri.fsPath)
		}

		return Promise.race([
			this.initRootPathContext(document, position),
			createTimeoutPromise(timeout, this.getRootPathContextFromCache(document.uri.fsPath)),
		])
	}
}
