function decorate(
	decorator: (fn: (...args: any[]) => any, key: string) => (...args: any[]) => any,
): (...args: any[]) => any {
	return (_target: any, key: string, descriptor: any) => {
		let fnKey: string | null = null
		let fn: ((...args: any[]) => any) | null = null

		if (typeof descriptor.value === "function") {
			fnKey = "value"
			fn = descriptor.value
		} else if (typeof descriptor.get === "function") {
			fnKey = "get"
			fn = descriptor.get
		}

		if (!fn || !fnKey) {
			throw new Error("not supported")
		}

		descriptor[fnKey] = decorator(fn, key)
	}
}

// 防抖
export function debounce(delay: number): (...args: any[]) => any {
	return decorate((fn, key) => {
		const timerKey = `$debounce$${key}`

		return function (this: any, ...args: any[]) {
			clearTimeout(this[timerKey])
			this[timerKey] = setTimeout(() => fn.apply(this, args), delay)
		}
	})
}
