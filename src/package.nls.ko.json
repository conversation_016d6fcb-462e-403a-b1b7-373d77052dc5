{"extension.displayName": "Zhanlu Code (이전 Roo Cline)", "extension.description": "에디터에서 작동하는 AI 에이전트 개발팀.", "command.newTask.title": "새 작업", "command.explainCode.title": "코드 설명", "command.fixCode.title": "코드 수정", "command.improveCode.title": "코드 개선", "command.addToContext.title": "컨텍스트에 추가", "command.openInNewTab.title": "새 탭에서 열기", "command.focusInput.title": "입력 필드 포커스", "command.setCustomStoragePath.title": "사용자 지정 저장소 경로 설정", "command.importSettings.title": "설정 가져오기", "command.terminal.addToContext.title": "터미널 내용을 컨텍스트에 추가", "command.terminal.fixCommand.title": "이 명령어 수정", "command.terminal.explainCommand.title": "이 명령어 설명", "command.acceptInput.title": "입력/제안 수락", "views.activitybar.title": "Zhanlu Code", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.mcpServers.title": "MCP 서버", "command.prompts.title": "모드", "command.history.title": "기록", "command.marketplace.title": "마켓플레이스", "command.openInEditor.title": "에디터에서 열기", "command.settings.title": "설정", "command.documentation.title": "문서", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "'항상 실행 작업 승인' 이 활성화되어 있을 때 자동으로 실행할 수 있는 명령어", "commands.deniedCommands.description": "승인을 요청하지 않고 자동으로 거부될 명령어 접두사. 허용된 명령어와 충돌하는 경우 가장 긴 접두사 일치가 우선됩니다. 모든 명령어를 거부하려면 *를 추가하세요.", "commands.commandExecutionTimeout.description": "명령어 실행이 완료되기를 기다리는 최대 시간(초), 타임아웃 전까지 (0 = 타임아웃 없음, 1-600초, 기본값: 0초)", "commands.commandTimeoutAllowlist.description": "명령어 실행 타임아웃에서 제외되는 명령어 접두사. 이러한 접두사와 일치하는 명령어는 타임아웃 제한 없이 실행됩니다.", "settings.vsCodeLmModelSelector.description": "VSCode 언어 모델 API 설정", "settings.vsCodeLmModelSelector.vendor.description": "언어 모델 공급자 (예: copilot)", "settings.vsCodeLmModelSelector.family.description": "언어 모델 계열 (예: gpt-4)", "settings.customStoragePath.description": "사용자 지정 저장소 경로. 기본 위치를 사용하려면 비워두세요. 절대 경로를 지원합니다 (예: 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Zhanlu Code 빠른 수정 사용 설정", "settings.autoImportSettingsPath.description": "확장 프로그램 시작 시 자동으로 가져올 Zhanlu 구성 파일의 경로입니다. 절대 경로 및 홈 디렉토리에 대한 상대 경로를 지원합니다(예: '~/Documents/zhanlu-settings.json'). 자동 가져오기를 비활성화하려면 비워 둡니다.", "settings.useAgentRules.description": "에이전트별 규칙에 대한 AGENTS.md 파일 로드를 활성화합니다 (참조: https://agent-rules.org/)"}