{"extension.displayName": "Zhanlu Code (önceden Roo Cline)", "extension.description": "Düzenleyicinde tam bir AI ajanları geliştirme ekibi.", "command.newTask.title": "<PERSON><PERSON>", "command.explainCode.title": "Kodu Açıkla", "command.fixCode.title": "<PERSON><PERSON>", "command.improveCode.title": "<PERSON><PERSON>tir", "command.addToContext.title": "Bağ<PERSON><PERSON>", "command.openInNewTab.title": "<PERSON><PERSON>", "command.focusInput.title": "<PERSON><PERSON><PERSON>", "command.setCustomStoragePath.title": "<PERSON><PERSON>", "command.importSettings.title": "Ayarları İçe Aktar", "command.terminal.addToContext.title": "Terminal İçeriğini Bağlama Ekle", "command.terminal.fixCommand.title": "<PERSON><PERSON> <PERSON><PERSON>", "command.terminal.explainCommand.title": "Bu Komutu Açıkla", "command.acceptInput.title": "Girişi/Öneriyi Kabul Et", "views.activitybar.title": "Zhanlu Code", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.mcpServers.title": "MCP Sunucuları", "command.prompts.title": "<PERSON><PERSON><PERSON>", "command.history.title": "Geçmiş", "command.marketplace.title": "Marketplace", "command.openInEditor.title": "Düzenleyicide Aç", "command.settings.title": "<PERSON><PERSON><PERSON>", "command.documentation.title": "Dokümantasyon", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "'Her zaman yür<PERSON><PERSON><PERSON> işlemlerini onayla' etkinleştirildiğinde otomatik olarak yürütülebilen komutlar", "commands.deniedCommands.description": "Onay istenmeden otomatik olarak reddedilecek komut önekleri. İzin verilen komutlarla çakışma durumunda en uzun önek eşleşmesi öncelik alır. Tüm komutları reddetmek için * ekleyin.", "commands.commandExecutionTimeout.description": "Komut yürütmesinin tamamlanmasını beklemek için maksimum süre (saniye), zaman aşımından önce (0 = zaman aşımı yok, 1-600s, varsayılan: 0s)", "commands.commandTimeoutAllowlist.description": "Komut yürütme zaman aşımından hariç tutulan komut önekleri. Bu öneklerle eşleşen komutlar zaman aşımı kısıtlamaları olmadan çalışacaktır.", "settings.vsCodeLmModelSelector.description": "VSCode dil modeli API'si için a<PERSON>", "settings.vsCodeLmModelSelector.vendor.description": "Dil modelinin <PERSON>ğlayıcısı (örn: copilot)", "settings.vsCodeLmModelSelector.family.description": "<PERSON>l modelinin a<PERSON> (örn: gpt-4)", "settings.customStoragePath.description": "Özel depolama yolu. Varsayılan konumu kullanmak için boş bırakın. Mutlak yolları destekler (örn: 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Zhanlu Code hızlı düzeltmeleri etkinleştir.", "settings.autoImportSettingsPath.description": "Uzantı başlangıcında otomatik olarak içe aktarılacak bir Zhanlu yapılandırma dosyasının yolu. Mutlak yolları ve ana dizine göreli yolları destekler (ör. '~/Documents/zhanlu-settings.json'). Otomatik içe aktarmayı devre dışı bırakmak için boş bırakın.", "settings.useAgentRules.description": "Aracıya özgü kurallar için AGENTS.md dosyalarının yüklenmesini etkinleştirin (bkz. https://agent-rules.org/)"}