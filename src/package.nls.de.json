{
	"extension.displayName": "zhan<PERSON>:AI Coding Assistant-Intelligent partner in development",
	"extension.description": "zhan<PERSON>:AI Coding Assistant-your intelligent partner in software development with automatic code generation",
	"command.newTask.title": "Neue Aufgabe",
	"command.explainCode.title": "Code Erklären",
	"command.fixCode.title": "Code Reparieren",
	"command.improveCode.title": "Code Verbessern",
	"command.unitTest.title": "Unit-Test",
	"command.codeReview.title": "Code-Bewertung",
	"command.commentCode.title": "Code Kommentieren",
	"command.addToContext.title": "Zum Kontext Hinzufügen",
	"command.openInNewTab.title": "In Neuem Tab Öffnen",
	"command.focusInput.title": "Eingabefeld Fokussieren",
	"command.setCustomStoragePath.title": "Benutzerdefinierten Speicherpfad Festlegen",
	"command.importSettings.title": "Einstellungen Importieren",
	"command.terminal.addToContext.title": "Terminal-Inhalt zum Kontext Hinzufügen",
	"command.terminal.fixCommand.title": "Diesen Befehl Reparieren",
	"command.terminal.explainCommand.title": "Diesen Befehl Erklären",
	"command.acceptInput.title": "Eingabe/Vorschlag Akzeptieren",
	"views.activitybar.title": "Zhanlu",
	"views.contextMenu.label": "Zhanlu",
	"views.terminalMenu.label": "Zhanlu",
	"views.sidebar.name": "Zhanlu",
	"command.mcpServers.title": "MCP Server",
	"command.prompts.title": "Modi",
	"command.history.title": "Verlauf",
	"command.marketplace.title": "Marktplatz",
	"command.openInEditor.title": "Im Editor Öffnen",
	"command.settings.title": "Einstellungen",
	"command.documentation.title": "Dokumentation",
	"command.logout.title": "Abmelden",
	"configuration.title": "Zhanlu",
	"commands.allowedCommands.description": "Befehle, die automatisch ausgeführt werden können, wenn 'Ausführungsoperationen immer genehmigen' aktiviert ist",
	"commands.deniedCommands.description": "Befehlspräfixe, die automatisch abgelehnt werden, ohne nach Genehmigung zu fragen. Bei Konflikten mit erlaubten Befehlen hat die längste Präfix-Übereinstimmung Vorrang. Füge * hinzu, um alle Befehle abzulehnen.",
	"commands.commandExecutionTimeout.description": "Maximale Zeit in Sekunden, die auf den Abschluss der Befehlsausführung gewartet wird, bevor ein Timeout auftritt (0 = kein Timeout, 1-600s, Standard: 0s)",
	"commands.commandTimeoutAllowlist.description": "Befehlspräfixe, die vom Timeout der Befehlsausführung ausgeschlossen sind. Befehle, die diesen Präfixen entsprechen, werden ohne Timeout-Beschränkungen ausgeführt.",
	"settings.vsCodeLmModelSelector.description": "Einstellungen für die VSCode-Sprachmodell-API",
	"settings.vsCodeLmModelSelector.vendor.description": "Der Anbieter des Sprachmodells (z.B. copilot)",
	"settings.vsCodeLmModelSelector.family.description": "Die Familie des Sprachmodells (z.B. gpt-4)",
<<<<<<< HEAD
	"settings.customStoragePath.description": "Benutzerdefinierter Speicherpfad. Leer lassen, um den Standardspeicherort zu verwenden. Unterstützt absolute Pfade (z.B. 'D:\\ZhanluStorage')",
	"settings.completion.enterprise_code_completion.description": "Unternehmenscode ergänzen",
	"settings.rooCodeCloudEnabled.description": "Aktiviere Ecloud Cloud.",
	"settings.completion.debounce_time.description": "Verzögerungszeit in Millisekunden (ms) für Code-Vervollständigung",
	"settings.completion.completion_number.description": "Anzahl der generierten Vervollständigungsvorschläge",
	"settings.completion.inlineCompletion_granularity.description": "Präferenz für Granularität der Vervollständigung",
	"settings.completion.inlineCompletion_granularity.singleRow": "Einzelzeile",
	"settings.completion.inlineCompletion_granularity.oneTimeMaximization": "Einmalige Maximierung",
	"settings.completion.inlineCompletion_granularity.balanced": "Ausgewogen",
	"settings.completion.multiple_line_Completion.description": "Mehrzeilige Code-Vervollständigungsmethode",
	"settings.completion.multiple_line_Completion.autoCompletion": "Automatische Vervollständigung",
	"settings.completion.multiple_line_Completion.triggerCompletion": "Ausgelöste Vervollständigung",
	"settings.completion.multiple_line_Completion.autoCompletion.description": "Enter und Ctrl+K (Cmd+K auf Mac) lösen mehrzeilige Vervollständigung aus",
	"settings.completion.multiple_line_Completion.triggerCompletion.description": "Nur Ctrl+K (Cmd+K auf Mac) löst mehrzeilige Vervollständigung aus, Enter nicht",
	"settings.completion.max_tokens_completion.description": "Maximale Token-Anzahl für Code-Vervollständigung",
	"settings.dmt.maxTokens.description": "max_tokens für multimodale Fragen und Antworten",
	"settings.serverBaseUrl.description": "Basis-URL für den Zhanlu-Server, standardmäßig ist https://api-wuxi-1.cmecloud.cn:8443"
=======
	"settings.customStoragePath.description": "Benutzerdefinierter Speicherpfad. Leer lassen, um den Standardspeicherort zu verwenden. Unterstützt absolute Pfade (z.B. 'D:\\RooCodeStorage')",
	"settings.enableCodeActions.description": "Roo Code Schnelle Problembehebung aktivieren.",
	"settings.autoImportSettingsPath.description": "Pfad zu einer RooCode-Konfigurationsdatei, die beim Start der Erweiterung automatisch importiert wird. Unterstützt absolute Pfade und Pfade relativ zum Home-Verzeichnis (z.B. '~/Documents/roo-code-settings.json'). Leer lassen, um den automatischen Import zu deaktivieren.",
	"settings.useAgentRules.description": "Aktiviert das Laden von AGENTS.md-Dateien für agentenspezifische Regeln (siehe https://agent-rules.org/)"
>>>>>>> upstream_v3.24.0
}
