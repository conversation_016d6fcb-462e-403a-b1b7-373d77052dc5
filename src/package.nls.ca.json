{"extension.displayName": "Zhanlu Code (abans Roo Cline)", "extension.description": "Un equip complet de desenvolupament d'agents d'IA al teu editor.", "command.newTask.title": "Nova Tasca", "command.explainCode.title": "Explicar Codi", "command.fixCode.title": "<PERSON><PERSON><PERSON><PERSON>", "command.improveCode.title": "Millorar Codi", "command.addToContext.title": "<PERSON><PERSON><PERSON>r al Context", "command.openInNewTab.title": "Obrir en una Nova Pestanya", "command.focusInput.title": "Enfocar Camp d'Entrada", "command.setCustomStoragePath.title": "Establir Ruta d'Emmagatzematge Personalitzada", "command.importSettings.title": "Importa<PERSON>", "command.terminal.addToContext.title": "Afegir Contingut del Terminal al Context", "command.terminal.fixCommand.title": "<PERSON><PERSON><PERSON><PERSON>", "command.terminal.explainCommand.title": "Explicar <PERSON><PERSON><PERSON>", "command.acceptInput.title": "Acceptar Entrada/Suggeriment", "views.activitybar.title": "Zhanlu Code", "views.contextMenu.label": "Zhanlu Code", "views.terminalMenu.label": "Zhanlu Code", "views.sidebar.name": "Zhanlu Code", "command.mcpServers.title": "Servidors MCP", "command.prompts.title": "Modes", "command.history.title": "Historial", "command.marketplace.title": "Mercat", "command.openInEditor.title": "Obrir a l'Editor", "command.settings.title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "command.documentation.title": "Documentació", "configuration.title": "Zhanlu Code", "commands.allowedCommands.description": "Ordres que es poden executar automàticament quan 'Aprova sempre les operacions d'execució' està activat", "commands.deniedCommands.description": "Prefixos d'ordres que seran automàticament denegats sense demanar aprovació. En cas de conflictes amb ordres permeses, la coincidència de prefix més llarga té prioritat. Afegeix * per denegar totes les ordres.", "commands.commandExecutionTimeout.description": "Temps màxim en segons per esperar que l'execució de l'ordre es completi abans d'esgotar el temps (0 = sense temps límit, 1-600s, per defecte: 0s)", "commands.commandTimeoutAllowlist.description": "Prefixos d'ordres que estan exclosos del temps límit d'execució d'ordres. Les ordres que coincideixin amb aquests prefixos s'executaran sense restriccions de temps límit.", "settings.vsCodeLmModelSelector.description": "Configuració per a l'API del model de llenguatge VSCode", "settings.vsCodeLmModelSelector.vendor.description": "El proveïdor del model de llenguatge (p. ex. copilot)", "settings.vsCodeLmModelSelector.family.description": "La família del model de llenguatge (p. ex. gpt-4)", "settings.customStoragePath.description": "Ruta d'emmagatzematge personalitzada. Deixeu-la buida per utilitzar la ubicació predeterminada. Admet rutes absolutes (p. ex. 'D:\\ZhanluStorage')", "settings.enableCodeActions.description": "Habilitar correccions ràpides de Zhanlu Code.", "settings.autoImportSettingsPath.description": "Ruta a un fitxer de configuració de Zhanlu per importar automàticament en iniciar l'extensió. Admet rutes absolutes i rutes relatives al directori d'inici (per exemple, '~/Documents/zhanlu-settings.json'). Deixeu-ho en blanc per desactivar la importació automàtica.", "settings.useAgentRules.description": "Activa la càrrega de fitxers AGENTS.md per a regles específiques de l'agent (vegeu https://agent-rules.org/)"}