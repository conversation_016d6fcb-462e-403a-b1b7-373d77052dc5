{"name": "@roo-code/web-roo-code", "version": "0.0.0", "type": "module", "scripts": {"lint": "next lint --max-warnings 0", "check-types": "tsc --noEmit", "dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-slot": "^1.2.3", "@roo-code/evals": "workspace:^", "@roo-code/types": "workspace:^", "@tanstack/react-query": "^5.79.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-auto-scroll": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.15.0", "lucide-react": "^0.518.0", "next": "^15.2.5", "next-themes": "^0.4.6", "posthog-js": "^1.248.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "recharts": "^2.15.3", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.61"}, "devDependencies": {"@roo-code/config-eslint": "workspace:^", "@roo-code/config-typescript": "workspace:^", "@tailwindcss/typography": "^0.5.16", "@types/node": "20.x", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "autoprefixer": "^10.4.21", "postcss": "^8.5.4", "tailwindcss": "^3.4.17"}}