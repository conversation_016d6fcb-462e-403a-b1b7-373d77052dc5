#!/bin/bash

# 创建临时目录存放导出的镜像
mkdir -p docker_images

# 定义镜像列表
declare -a images=(
    "roomote-worker:latest"
    "roomote-api:latest"
    "roomote-controller:latest"
    "roomote-base:latest"
    "roomote-dashboard:latest"
    "evals-runner:latest"
    "evals-web:latest"
    "postgres:17.5"
    "redis:7-alpine"
    "postgres:15.4"
)

# 并行导出所有镜像
for image in "${images[@]}"; do
    # 替换特殊字符为下划线作为文件名
    filename=$(echo "$image" | sed 's/[:\/]/_/g')
    echo "开始导出 $image 到 docker_images/${filename}.tar.gz"
    (docker save "$image" | gzip > "docker_images/${filename}.tar.gz") &
done

# 等待所有后台任务完成
wait

echo "所有镜像导出完成，开始打包..."

# 打包所有.tar.gz文件
tar czf all-docker-images.tar.gz docker_images/

# 清理临时文件
rm -rf docker_images/

echo "完成！所有镜像已打包到 all-docker-images.tar.gz"
