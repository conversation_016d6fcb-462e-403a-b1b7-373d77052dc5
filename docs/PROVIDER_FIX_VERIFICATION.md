# Provider Fix Verification Guide

This guide verifies that the OpenRouter provider configuration fix works correctly.

## Test Scenario: OpenRouter Evaluation

### Prerequisites

- Environment variables set for both Zhanlu and OpenRouter credentials
- EVAL_MODE=true in environment

### Step 1: Create OpenRouter Run via Web Interface

1. Navigate to `http://localhost:3000/runs/new`
2. Select "OpenRouter" tab
3. Choose an OpenRouter model (e.g., "gpt-4-turbo")
4. Set concurrency to 1
5. Select "Some" exercises and choose a simple exercise
6. Add description: "Testing OpenRouter provider fix"
7. Click "Launch"

**Expected Result**: Run should be created successfully with settings like:

```json
{
	"apiProvider": "openrouter",
	"openRouterModelId": "gpt-4-turbo",
	"openRouterApiKey": "sk-or-v1-...",
	"diffEnabled": true
}
```

### Step 2: CLI Starts Evaluation

The CLI will:

1. Read the run configuration from database
2. Create `settings.json` in exercises directory with merged settings
3. Open VS Code in the exercise directory
4. Start the plugin with auto-login

**Expected `settings.json` content**:

```json
{
	"apiProvider": "openrouter",
	"openRouterModelId": "gpt-4-turbo",
	"openRouterApiKey": "sk-or-v1-...",
	"diffEnabled": true,
	"fuzzyMatchThreshold": 1,
	"rateLimitSeconds": 0
}
```

### Step 3: Auto-Login Profile Creation

When the plugin starts:

1. **Auto-auth loads config**: `loadAutoAuthConfig()` reads both environment variables AND `settings.json`
2. **Smart profile creation**: Auto-login detects `apiProvider: "openrouter"` in settings
3. **Correct profile created**: Creates profile named `eval-openrouter` with OpenRouter configuration
4. **Profile activated**: Plugin uses OpenRouter provider for API calls

**Expected Plugin State**:

- Current profile: `eval-openrouter`
- Provider: OpenRouter
- Model: gpt-4-turbo
- API Key: OpenRouter key (not Zhanlu key)

### Step 4: Evaluation Execution

During evaluation:

1. Plugin makes API calls to OpenRouter endpoints
2. Uses OpenRouter model ID
3. Uses OpenRouter API key
4. No Zhanlu provider conflicts

**Expected API Calls**:

- Endpoint: `https://openrouter.ai/api/v1/chat/completions`
- Model: `gpt-4-turbo`
- Authorization: `Bearer sk-or-v1-...`

## Verification Checkpoints

### ✅ Web Interface Fix

- [ ] OpenRouter selection sets `apiProvider: "openrouter"`
- [ ] Model selection sets correct `openRouterModelId`
- [ ] Run settings include both provider and model

### ✅ Auto-Login Enhancement

- [ ] Plugin reads `settings.json` from workspace
- [ ] Auto-login detects non-zhanlu provider from settings
- [ ] Correct profile created based on evaluation settings

### ✅ Profile Management

- [ ] Plugin uses `eval-openrouter` profile (not `zhanlu`)
- [ ] Profile contains OpenRouter configuration
- [ ] API calls go to OpenRouter (not Zhanlu)

### ✅ End-to-End Flow

- [ ] Evaluation completes successfully
- [ ] No provider/model mismatch errors
- [ ] OpenRouter model performs evaluation task
- [ ] Results saved with correct provider attribution

## Troubleshooting

### Issue: Still uses Zhanlu provider

**Check**: Verify `settings.json` contains `"apiProvider": "openrouter"`
**Solution**: Re-run evaluation, check web interface sets provider correctly

### Issue: Profile not created correctly

**Check**: VS Code extension logs for auto-login messages
**Solution**: Verify workspace contains `settings.json` file

### Issue: API key errors

**Check**: Environment has both `ZHANLU_*` and `OPENROUTER_API_KEY` variables
**Solution**: Verify OpenRouter API key is valid and has credits

## Success Criteria

The fix is working correctly when:

1. ✅ **Web Interface**: OpenRouter selection includes `apiProvider` in settings
2. ✅ **Auto-Login**: Creates OpenRouter profile automatically during evaluation
3. ✅ **Evaluation**: Uses OpenRouter API endpoints throughout
4. ✅ **Results**: Evaluation completes with OpenRouter model responses
5. ✅ **Isolation**: Normal plugin usage (non-evaluation) unaffected

## Regression Testing

Verify other providers still work:

### Zhanlu Provider Test

1. Create evaluation with Zhanlu model
2. Verify auto-login creates Zhanlu profile
3. Confirm Zhanlu API calls work

### Import Settings Test

1. Import settings.json with different provider
2. Verify provider configuration is respected
3. Confirm no conflicts with auto-login

This comprehensive verification ensures the fix works correctly across all scenarios.
