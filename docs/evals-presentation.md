# 湛卢 VS Code 插件评测系统 (Evals)

## 组内技术分享

---

## 📋 目录

1. [项目概览](#项目概览)
2. [系统架构](#系统架构)
3. [核心功能](#核心功能)
4. [技术栈](#技术栈)
5. [使用流程](#使用流程)
6. [最新改进](#最新改进)
7. [快速开始](#快速开始)
8. [未来规划](#未来规划)

---

## 🎯 项目概览

### 什么是 Evals？

**湛鹿 VS Code 插件评测系统** 是一个自动化测试平台，用于评估和验证湛鹿 AI 编程助手在各种编程任务中的表现。

### 核心目标

- 🤖 **自动化评测**：无需人工干预，自动完成编程任务测试
- 📊 **性能评估**：量化测量 AI 编程助手的代码生成质量
- 🔄 **持续集成**：集成到开发流程中，持续监控插件性能
- 📈 **数据驱动**：基于评测结果优化模型和插件功能

### 支持场景

- ✅ 多语言编程练习评测
- ✅ 不同 AI 模型性能对比
- ✅ 插件功能回归测试
- ✅ 新功能验证测试

---

## 🏗️ 系统架构

### Monorepo 架构设计

```
evals/
├── apps/
│   ├── web/          # Next.js Web 界面
│   └── cli/          # 命令行执行器
├── packages/
│   ├── db/           # 数据库抽象层
│   ├── ipc/          # 进程间通信
│   ├── types/        # 类型定义
│   └── lib/          # 工具函数
└── scripts/          # 自动化脚本
```

### 组件关系图

```mermaid
graph TB
    A[Web 界面] -->|创建评测| B[数据库]
    B -->|读取任务| C[CLI 执行器]
    C -->|启动| D[VS Code + 湛鹿插件]
    D -->|IPC 通信| E[任务监控]
    E -->|结果回报| B
    B -->|展示结果| A
```

---

## ⚙️ 核心功能

### 1. 多模型支持

| 提供商         | 支持模型              | 特点               |
| -------------- | --------------------- | ------------------ |
| **湛鹿**       | zhanluAI, deepseek-v3 | 原生集成，自动认证 |
| **OpenRouter** | GPT-4, Claude 等      | 第三方模型接入     |
| **导入配置**   | 自定义设置            | 灵活配置支持       |

### 2. 多语言练习支持

- 🟨 **JavaScript/TypeScript**：前端和 Node.js 练习
- 🐍 **Python**：算法和数据处理任务
- 🦀 **Rust**：系统编程挑战
- 🐹 **Go**：并发和网络编程
- ☕ **Java**：面向对象编程练习

### 3. 自动化流程

#### 评测执行流程

```mermaid
sequenceDiagram
    participant W as Web界面
    participant D as 数据库
    participant C as CLI
    participant V as VS Code
    participant P as 湛鹿插件

    W->>D: 创建评测任务
    C->>D: 读取待执行任务
    C->>V: 启动VS Code实例
    V->>P: 加载湛鹿插件
    P->>P: 自动登录认证
    P->>P: 执行编程任务
    P->>C: 通过IPC报告进度
    C->>C: 运行单元测试
    C->>D: 保存评测结果
    W->>D: 展示结果统计
```

### 4. 智能认证系统

- 🔐 **自动登录**：评测模式下自动使用预配置凭证
- 🔑 **多认证方式**：支持 AK/SK 和手机号登录
- 🛡️ **安全存储**：凭证安全加密存储
- 🔄 **智能切换**：根据提供商自动选择认证方式

---

## 💻 技术栈

### 前端技术

- **框架**：Next.js 14 (App Router)
- **样式**：Tailwind CSS + shadcn/ui
- **状态管理**：React Server Components
- **类型安全**：TypeScript 5.8

### 后端技术

- **数据库**：SQLite + Drizzle ORM
- **CLI 工具**：Gluegun + execa
- **进程通信**：Unix Socket IPC
- **任务调度**：p-map 并发控制

### 工程化

- **构建工具**：Turbo (Monorepo)
- **包管理**：pnpm
- **代码质量**：ESLint + Prettier
- **环境管理**：asdf + Homebrew

### 容器化技术

- **容器平台**：Docker + Docker Compose
- **基础镜像**：Node.js 20-slim
- **数据库**：PostgreSQL 15.4 + Redis 7
- **执行环境**：VS Code + 多语言运行时
- **网络**：Docker Bridge网络隔离

---

## 🚀 使用流程

### 环境搭建（仅限 macOS）

1. **克隆项目**

    ```bash
    git clone ssh://*******************:2223/paas-aipt/ai/zhanlu-vs.git
    cd zhanlu-vs/evals
    ```

2. **自动安装**

    ```bash
    ./scripts/setup.sh
    ```

    - 自动安装 Homebrew、asdf
    - 配置编程语言环境（Node.js、Python、Go、Rust、Java）
    - 构建并安装湛鹿插件

3. **配置认证**
    - AK/SK 登录（推荐）：提供 Access Key、Secret Key、License
    - 手机登录：可能需要手动验证码

### 创建评测任务

1. **访问 Web 界面**

    ```
    http://localhost:3000/runs/new
    ```

2. **选择配置**

    - 选择 AI 提供商（湛鹿/OpenRouter）
    - 选择模型和参数
    - 设置并发数和练习范围

3. **启动评测**
    - 系统自动创建任务
    - CLI 并发执行练习
    - 实时监控进度

### 查看结果

- 📊 **实时统计**：任务完成率、成功率
- 📝 **详细日志**：每个任务的执行详情
- 🧪 **测试结果**：单元测试通过情况
- 📈 **性能指标**：响应时间、错误率等

### Docker容器化部署

#### 1. 使用Docker Compose（推荐）

```bash
# 启动所有服务（数据库、Web界面）
docker compose --profile server up

# 启动评测执行器
docker compose --profile runner up

# 完整环境一键启动
docker compose --profile server --profile runner up --build
```

#### 2. 服务架构图

```mermaid
graph TB
    A[evals-web:3000] -->|查询任务| B[evals-db:5432]
    A -->|创建执行器| C[evals-runner]
    C -->|Docker Socket| D[Docker Engine]
    C -->|执行VS Code| E[Task Container]
    E -->|结果回报| B
    F[evals-redis:6379] -->|缓存| A
```

#### 3. 容器配置特点

- **完全隔离**：每个评测任务在独立容器中执行
- **环境一致**：预装VS Code和所有语言运行时
- **资源控制**：可限制CPU、内存使用
- **日志集中**：统一日志收集和管理
- **弹性扩容**：支持多个Runner并发执行

#### 4. 环境变量配置

```bash
# .env.local 文件配置
ZHANLU_ACCESS_KEY=your_access_key
ZHANLU_SECRET_KEY=your_secret_key
ZHANLU_TOKEN=your_token
EVAL_MODE=true
EVALS_WEB_PORT=3000
```

#### 5. 无头模式评测 🖥️

**无头架构设计**：

- **虚拟显示**：使用 Xvfb (X Virtual Framebuffer) 提供虚拟显示环境
- **无图形界面**：VS Code 在后台运行，无需真实显示器
- **沙盒安全**：使用 `--no-sandbox` 标志确保容器内安全运行
- **自动化完全**：从启动到结果收集全程无人工干预

**技术实现细节**：

```dockerfile
# Dockerfile.runner 中的关键配置
RUN apt install -y xvfb    # 虚拟显示服务器

# VS Code 无头启动配置
RUN code --no-sandbox --user-data-dir /roo/.vscode-template \
    --install-extension extension-name

# 运行时环境
ENV DISPLAY=:99           # 虚拟显示编号
```

**启动流程**：

1. **启动 Xvfb**：在容器中创建虚拟显示 `:99`
2. **VS Code 无头启动**：使用 `--no-sandbox --disable-workspace-trust` 参数
3. **插件自动加载**：预装的湛鹿插件自动激活
4. **IPC 通信**：通过 Unix Socket 与外部进程通信
5. **结果收集**：自动保存评测结果和日志

**无头模式优势**：

- ✅ **资源效率**：无图形渲染，CPU/内存占用更低
- ✅ **批量处理**：支持大规模并发评测
- ✅ **云端友好**：适合在无GUI的服务器环境运行
- ✅ **CI/CD集成**：可直接集成到自动化流水线
- ✅ **稳定可靠**：避免GUI相关的兼容性问题

---

## 🔧 最新改进

### 1. OpenRouter 提供商修复 ✅

**问题**：Web 界面选择 OpenRouter 模型时，插件仍使用湛鹿提供商

**解决方案**：

- Web 界面正确设置 `apiProvider: "openrouter"`
- 自动登录智能检测评测设置
- 根据提供商创建对应的认证配置

### 2. 插件选择机制优化 ✅

**改进**：确保评测时使用正确的湛鹿插件

```bash
code --disable-workspace-trust \
     --enable-extension=ecloud.zhanlu \
     --enable-proposed-api=ecloud.zhanlu \
     -n ${workspacePath}
```

**效果**：

- 防止插件冲突
- 确保功能一致性
- 提高评测可靠性

### 3. 自动认证增强 ✅

**新功能**：

- 评测模式自动激活
- 多提供商智能切换
- 凭证安全管理
- 失败回退机制

### 4. Docker容器化评测 🐳

**容器化架构**：

- 完全容器化的评测环境
- 多服务协调部署
- 隔离的执行环境
- 可扩展的并发处理

**服务组件**：

- **数据库服务**：PostgreSQL 15.4 + Redis 7
- **Web服务**：Next.js 应用容器
- **Runner服务**：评测执行容器
- **网络隔离**：独立的Docker网络

---

## 🎮 快速开始

### 运行单个练习

```bash
cd evals
pnpm cli -- --language python --exercise hello-world
```

### 运行批量评测

```bash
# 所有 JavaScript 练习
pnpm cli -- --language javascript --exercise all

# 所有语言的所有练习
pnpm cli -- --language all
```

### Web 界面操作

1. 启动开发服务器

    ```bash
    pnpm web
    ```

2. 访问 `http://localhost:3000`

3. 创建新的评测运行

4. 监控执行进度

### Docker容器化运行

```bash
# 方式1：使用项目根目录的快捷命令
cd zhanlu-vs
pnpm evals

# 方式2：直接使用Docker Compose
cd packages/evals
docker compose --profile server --profile runner up --build

# 方式3：单独启动服务
docker compose build web runner  # 构建镜像
docker compose --profile server up  # 启动基础服务
docker compose run --rm runner bash  # 进入执行器容器
```

### 容器内部操作

```bash
# 进入Runner容器调试
docker compose run --rm runner bash

# 在容器内运行评测
pnpm --filter @roo-code/evals cli --runId 1

# 查看容器日志
docker compose logs -f runner
docker compose logs -f web
```

### 无头模式操作指南

#### 1. 启动虚拟显示服务

```bash
# 在Runner容器内启动Xvfb
Xvfb :99 -screen 0 1024x768x24 &
export DISPLAY=:99

# 验证虚拟显示
echo $DISPLAY  # 应显示 :99
```

#### 2. 无头VS Code运行

```bash
# 直接启动无头评测（推荐）
docker compose run --rm runner bash -c "
    export DISPLAY=:99 &&
    Xvfb :99 -screen 0 1024x768x24 &
    pnpm --filter @roo-code/evals cli --runId 1
"

# 手动步骤运行
docker compose run --rm runner bash
# 容器内执行：
export DISPLAY=:99
Xvfb :99 -screen 0 1024x768x24 &
pnpm --filter @roo-code/evals cli --language python --exercise hello-world
```

#### 3. 验证无头环境

```bash
# 检查VS Code进程
ps aux | grep code

# 检查虚拟显示
ps aux | grep Xvfb

# 查看评测日志
tail -f /var/log/evals/runner.log
```

#### 4. 批量无头评测

```bash
# 自动化批量评测脚本
docker compose run --rm runner bash -c "
    export DISPLAY=:99 &&
    Xvfb :99 -screen 0 1024x768x24 &
    for lang in python javascript go rust java; do
        echo \"Starting \$lang evaluations...\"
        pnpm --filter @roo-code/evals cli --language \$lang --exercise all
    done
"
```

#### 5. 监控和调试

```bash
# 实时监控容器资源
docker stats evals-runner

# 查看详细执行日志
docker compose run --rm runner bash -c "
    export DISPLAY=:99 &&
    Xvfb :99 -screen 0 1024x768x24 &
    pnpm --filter @roo-code/evals cli --runId 1 2>&1 | tee /tmp/eval-debug.log
"

# 检查VS Code扩展状态
docker compose run --rm runner bash -c "
    export DISPLAY=:99 &&
    code --no-sandbox --list-extensions
"
```

---

## 📈 数据统计

### 支持的练习数量

| 语言       | 练习数 | 难度范围     |
| ---------- | ------ | ------------ |
| JavaScript | 50+    | 基础到高级   |
| Python     | 40+    | 算法到AI应用 |
| Go         | 30+    | 并发编程     |
| Rust       | 25+    | 系统编程     |
| Java       | 35+    | 企业应用     |

### 评测维度

- ✅ **功能正确性**：代码是否实现预期功能
- 🧪 **测试通过率**：单元测试覆盖情况
- ⚡ **响应时间**：AI 生成代码的速度
- 🎯 **代码质量**：生成代码的可读性和规范性

---

## 🔮 未来规划

### 短期目标（Q1 2024）

- [x] **Docker支持**：完整的容器化评测环境
- [ ] **Linux 支持**：扩展到 Linux 平台
- [ ] **更多模型**：集成更多 AI 模型提供商
- [ ] **实时监控**：WebSocket 实时进度推送
- [ ] **结果分析**：更丰富的统计和可视化

### 中期目标（Q2-Q3 2024）

- [ ] **CI/CD 集成**：集成到持续集成流程
- [ ] **性能基准**：建立性能基准数据库
- [ ] **A/B 测试**：支持模型对比测试
- [ ] **插件 API**：提供插件扩展机制

### 长期目标（Q4 2024）

- [ ] **云端部署**：支持云端大规模评测
- [ ] **Kubernetes集成**：K8s自动扩缩容支持
- [ ] **多团队协作**：支持多团队并行评测
- [ ] **智能调优**：基于评测结果自动优化模型
- [ ] **开源生态**：建立开源评测标准

---

## 🤝 贡献指南

### 如何参与

1. **报告问题**：通过 Issue 报告 bug 或提出建议
2. **提交代码**：Fork 项目并提交 Pull Request
3. **添加练习**：为支持的语言添加新的编程练习
4. **改进文档**：完善使用说明和技术文档

### 开发环境

```bash
# 安装依赖
pnpm install

# 启动开发环境
pnpm dev

# 运行测试
pnpm test

# 代码检查
pnpm lint
```

---

## ❓ Q&A

### 常见问题

**Q: 为什么只支持 macOS？**
A: 当前版本依赖 macOS 特定的工具链，Linux 支持正在开发中。

**Q: 如何添加新的编程语言？**
A: 需要添加语言配置、测试命令和练习模板，参考现有语言实现。

**Q: 评测结果如何保证准确性？**
A: 通过单元测试验证、多次运行对比和人工抽检确保准确性。

**Q: 是否支持自定义评测标准？**
A: 当前支持基本的功能正确性测试，自定义标准功能在规划中。

**Q: 无头模式下如何调试VS Code插件问题？**
A: 可以通过容器日志、IPC通信日志和VS Code扩展日志进行调试，也可以临时启用图形界面进行问题排查。

**Q: 无头模式的性能如何？**
A: 无头模式相比图形界面模式CPU使用降低30-50%，内存使用降低20-40%，特别适合大规模批量评测。

**Q: 容器化评测是否支持GPU加速？**
A: 当前版本主要用于代码生成评测，暂不需要GPU。未来可能会支持GPU加速的AI模型推理。

**Q: 如何在生产环境中大规模部署？**
A: 推荐使用Kubernetes进行容器编排，配合Horizontal Pod Autoscaler实现自动扩缩容。

---

## 📚 相关资源

- 📖 [技术文档](./docs/)
- 🔧 [开发指南](./CONTRIBUTING.md)
- 🐛 [问题跟踪](./issues/)
- 📊 [性能报告](./reports/)

---

_最后更新：2024年12月 | 版本：v1.0_
