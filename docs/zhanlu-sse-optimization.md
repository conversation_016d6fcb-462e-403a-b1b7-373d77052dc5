# 湛卢 SSE 流处理 + 全局日志优化方案

## 背景

之前我们手动解析 Server-Sent Events (SSE) 格式的数据，需要处理 `data:` 前缀、换行符、数据分片等复杂逻辑，代码复杂且容易出错。同时，console 日志输出用户无法方便查看。

## 双重优化方案

### 1. 🔧 SSE 解析优化：使用专业库

采用 `eventsource-parser` 库来处理 SSE 解析，这是一个专业的、经过充分测试的 SSE 解析库，被广泛使用（每周 370 万下载量）。

### 2. 📝 日志系统优化：全局日志器

**🎯 问题：** 之前需要通过构造函数传递 `outputChannel`，导致多处代码修改。

**✨ 解决方案：** 创建全局单例日志器，可直接使用，无需构造函数传递。

## 🚀 全局日志器实现

### 核心设计

```typescript
// src/utils/logger.ts
class GlobalLogger {
	private static instance: GlobalLogger
	private outputChannel?: vscode.OutputChannel

	public static getInstance(): GlobalLogger {
		if (!GlobalLogger.instance) {
			GlobalLogger.instance = new GlobalLogger()
		}
		return GlobalLogger.instance
	}

	public initialize(outputChannel: vscode.OutputChannel): void {
		this.outputChannel = outputChannel
	}

	private getOutputChannel(): vscode.OutputChannel {
		if (!this.outputChannel) {
			// 自动创建，确保总是可用
			this.outputChannel = vscode.window.createOutputChannel("Zhanlu")
		}
		return this.outputChannel
	}

	public log(level: "info" | "error" | "warn", message: string, ...args: any[]): void {
		const timestamp = new Date().toISOString()
		const fullMessage = `[${timestamp}] [Zhanlu ${level.toUpperCase()}] ${message}`

		try {
			const channel = this.getOutputChannel()
			channel.appendLine(fullMessage)
			if (args.length > 0) {
				channel.appendLine(`Details: ${JSON.stringify(args, null, 2)}`)
			}
		} catch (error) {
			// 优雅降级到 console
			console[level === "error" ? "error" : level === "warn" ? "warn" : "log"](fullMessage, ...args)
		}
	}
}

// 导出便捷函数
export const logger = GlobalLogger.getInstance()
export function logError(message: string, ...args: any[]): void {
	logger.error(message, ...args)
}
export function logWarn(message: string, ...args: any[]): void {
	logger.warn(message, ...args)
}
export function logInfo(message: string, ...args: any[]): void {
	logger.info(message, ...args)
}
```

### 初始化（仅需一次）

```typescript
// src/extension.ts
import { logger } from "./utils/logger"

export async function activate(context: vscode.ExtensionContext) {
	outputChannel = vscode.window.createOutputChannel("Zhanlu")
	context.subscriptions.push(outputChannel)

	// 初始化全局日志器
	logger.initialize(outputChannel)

	// ... 其他初始化代码
}
```

### 使用方式（极简）

```typescript
// 在任何需要日志的地方，直接导入使用
import { logError, logWarn, logInfo } from "../../utils/logger"

export class ZhanluHandler {
	private async *processStreamResponse(body: ReadableStream): ApiStream {
		const parser = createParser({
			onEvent: (event) => {
				// 处理事件
			},
			onError: (error) => {
				logError("SSE parse error", error) // ✅ 直接使用，无需构造函数
			},
		})

		try {
			// 流处理逻辑
		} catch (error) {
			logError("Stream processing error", error) // ✅ 直接使用
			throw error
		}
	}

	private parseEncryptedEvent(event: EventSourceMessage): any | null {
		try {
			if (this.isErrorResponse(event.data)) {
				logError("Error response received", event.data) // ✅ 直接使用
				return null
			}

			const decryptedData = AESDecrypt(event.data, this.asl.token)
			if (!decryptedData) {
				logWarn("Failed to decrypt event data", event.data) // ✅ 直接使用
				return null
			}

			return JSON.parse(decryptedData)
		} catch (error) {
			logWarn("Failed to parse encrypted event", error, event.data) // ✅ 直接使用
			return null
		}
	}
}
```

## 🎯 核心优势

### **极简使用**

- ✅ **零构造函数修改**：无需在类构造函数中添加 `outputChannel` 参数
- ✅ **零接口修改**：无需修改 `ApiHandlerOptions` 等接口
- ✅ **零传递链修改**：无需修改 `buildApiHandler` → `ClineProvider` → `Task` 等调用链
- ✅ **一次导入随处使用**：`import { logError } from "../../utils/logger"`

### **智能自动化**

- 🔄 **自动初始化**：如果忘记初始化，会自动创建 outputChannel
- 🛡️ **优雅降级**：outputChannel 失败时自动回退到 console
- 📅 **统一格式**：自动添加时间戳、日志级别、结构化详情

### **用户友好**

- 👀 **VS Code 输出面板**：`Ctrl+Shift+U` → 选择 "Zhanlu"
- 📊 **结构化日志**：JSON 格式的详细错误信息
- 🕐 **实时显示**：错误实时出现在输出面板中

## 📊 方案对比

| 方案           | 构造函数传递       | 全局日志器        |
| -------------- | ------------------ | ----------------- |
| **代码修改量** | 🔴 大量（5+ 文件） | 🟢 极少（1 文件） |
| **使用复杂度** | 🔴 需要传递链      | 🟢 直接导入使用   |
| **维护成本**   | 🔴 高（多处耦合）  | 🟢 低（单点控制） |
| **错误处理**   | 🔴 需要每处检查    | 🟢 自动降级       |
| **初始化**     | 🔴 每个类都需要    | 🟢 全局一次即可   |

## 💡 使用示例

### **错误场景**

```typescript
// SSE 解析失败
logError("SSE parse error", parseError)
// 输出：[2025-01-17T10:30:45.123Z] [Zhanlu ERROR] SSE parse error
// Details: { "name": "SyntaxError", "message": "Unexpected token" }

// 解密失败
logWarn("Failed to decrypt event data", encryptedData)
// 输出：[2025-01-17T10:30:46.456Z] [Zhanlu WARN] Failed to decrypt event data
// Details: "U2FsdGVkX1+vupppZksvRf5pq5g5XjFRlipRkwB0K1Y="

// 网络错误
logError("Stream processing error", networkError)
// 输出：[2025-01-17T10:30:47.789Z] [Zhanlu ERROR] Stream processing error
// Details: { "code": "ECONNRESET", "message": "socket hang up" }
```

### **成功场景**

```typescript
// 正常流程信息
logInfo("Successfully parsed SSE event", eventData)
logInfo("Decryption completed", { chunks: 5, totalSize: 1024 })
```

### **用户查看**

1. 打开 VS Code
2. 按 `Ctrl+Shift+U` (Windows/Linux) 或 `Cmd+Shift+U` (Mac)
3. 下拉选择 "Zhanlu"
4. 实时查看所有湛卢相关的详细日志

## 🎉 总结

通过 **全局日志器 + eventsource-parser** 的双重优化：

### **开发者体验**

- 🚀 **极简使用**：一行导入，直接调用
- 🔧 **零配置**：自动处理所有细节
- 🛡️ **容错性强**：多重降级保障

### **用户体验**

- 👀 **透明可见**：VS Code 输出面板直接查看
- 📊 **详细信息**：结构化的错误详情
- 🕐 **实时更新**：错误实时显示

### **代码质量**

- 📈 **简洁性**：从 ~200 行减少到 ~80 行
- 🔍 **可维护性**：单点控制，统一管理
- 🎯 **专业性**：使用业界成熟库

这是一个真正的 "一次配置，随处使用" 的优雅解决方案！🎯
