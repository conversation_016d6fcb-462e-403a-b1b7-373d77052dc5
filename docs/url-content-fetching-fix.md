# URL内容获取修复

## 问题

使用 '@' URL 提及功能时可能遇到以下错误：

```
Error fetching content: An `executablePath` or `channel` must be specified for `puppeteer-core`
```

## 原因

该错误是由于 `puppeteer-chromium-resolver` 无法从默认的 Google 服务器下载 Chromium 浏览器导致的，通常在以下情况发生：

- 网络连接问题
- 防火墙限制
- 企业网络环境阻止下载

## 解决方案

已将 Chromium 下载源替换为国内镜像源，按优先级顺序：

1. `https://cdn.npmmirror.com/binaries` - npmmirror 镜像
2. `https://npm.taobao.org/mirrors` - 淘宝镜像
3. `https://storage.googleapis.com` - 原始 Google 源（备用）

## 下载位置

Chromium 会被下载到：

```
{VS Code 全局存储目录}/puppeteer/.chromium-browser-snapshots/
```

各平台的具体位置：

- **Windows:** `%APPDATA%\Code\User\globalStorage\roo-code.zhanlu\puppeteer\`
- **macOS:** `~/Library/Application Support/Code/User/globalStorage/roo-code.zhanlu/puppeteer/`
- **Linux:** `~/.config/Code/User/globalStorage/roo-code.zhanlu/puppeteer/`

## 故障排除

如果仍然遇到问题：

1. **重启 VS Code** - 有时需要重新启动才能生效
2. **清理存储** - 删除上述目录中的 `puppeteer` 文件夹，重新触发下载
3. **检查网络** - 确保能访问国内镜像源
4. **磁盘空间** - 确保有至少 200MB 的可用空间用于下载 Chromium

## 使用方法

修复后，URL 提及功能将正常工作：

- 在聊天中输入 `@https://example.com`
- 扩展会自动获取网页内容并转换为 Markdown 格式
- 内容会包含在对话中供 AI 分析

## 技术细节

修改的文件：

- `src/services/browser/UrlContentFetcher.ts`
- `src/services/browser/BrowserSession.ts`

使用的依赖：

- `puppeteer-core`: ^23.4.0
- `puppeteer-chromium-resolver`: ^23.0.0
